import axios from "axios";
import dotenv from "dotenv";
import { toTitleCase } from "../../../utils/titlecase.generator.js";

dotenv.config();

export const generateSnomedCtCode = async (term) => {
    try {
        console.log(`Generating Snomed data for: ${term}`);
        if (term) {
            const snomedBaseUrl = process.env.SNOMED_SERVER_BASE_URL;
            const response = await axios.get(`${snomedBaseUrl}/csnoserv/api/search/search`, {
                params: {
                    term: toTitleCase(term),
                    state: 'active'
                }
            });

            const results = response.data;
            const activeItem = results.find(item => item.activeStatus === 1);

            if (activeItem) {
                const { conceptId, term } = activeItem;

                // Validate that the conceptId is a valid SNOMED CT code (should be numeric)
                if (!/^\d+$/.test(conceptId)) {
                    console.warn(`Invalid SNOMED code format: ${conceptId} for term: ${term}`);
                    return getFallbackSnomedCode(term);
                }

                return { conceptId, term };
            } else {
                console.warn(`No active snomed item found with the term: ${term}`);
                return getFallbackSnomedCode(term);
            }
        } else {
            return "";
        }
    } catch (error) {
        console.error(`Error fetching Snomed data: ${term}`, error);
        return getFallbackSnomedCode(term);
    }
};

// Fallback function to provide valid SNOMED codes for common terms
const getFallbackSnomedCode = (term) => {
    const fallbackMap = {
        // Common medications
        'paracetamol': { conceptId: '387517004', term: 'Paracetamol' },
        'acetaminophen': { conceptId: '387517004', term: 'Paracetamol' },
        'ibuprofen': { conceptId: '387207008', term: 'Ibuprofen' },
        'aspirin': { conceptId: '387458008', term: 'Aspirin' },
        'amoxicillin': { conceptId: '27658006', term: 'Amoxicillin' },
        'metformin': { conceptId: '387467008', term: 'Metformin' },
        'omeprazole': { conceptId: '387137007', term: 'Omeprazole' },
        'atorvastatin': { conceptId: '387584000', term: 'Atorvastatin' },
        'amlodipine': { conceptId: '386864001', term: 'Amlodipine' },
        'lisinopril': { conceptId: '386873009', term: 'Lisinopril' },

        // Generic medication fallback
        'medication': { conceptId: '410942007', term: 'Drug or medicament' },
        'drug': { conceptId: '410942007', term: 'Drug or medicament' },
        'medicine': { conceptId: '410942007', term: 'Drug or medicament' },

        // Common conditions
        'hypertension': { conceptId: '38341003', term: 'Hypertension' },
        'diabetes': { conceptId: '73211009', term: 'Diabetes mellitus' },
        'fever': { conceptId: '386661006', term: 'Fever' },
        'headache': { conceptId: '25064002', term: 'Headache' },
        'cough': { conceptId: '49727002', term: 'Cough' },
        'cold': { conceptId: '82272006', term: 'Common cold' }
    };

    const normalizedTerm = term.toLowerCase().trim();

    // Try exact match first
    if (fallbackMap[normalizedTerm]) {
        console.log(`Using fallback SNOMED code for: ${term}`);
        return fallbackMap[normalizedTerm];
    }

    // Try partial matches for medications
    for (const [key, value] of Object.entries(fallbackMap)) {
        if (normalizedTerm.includes(key) || key.includes(normalizedTerm)) {
            console.log(`Using partial match fallback SNOMED code for: ${term} -> ${key}`);
            return value;
        }
    }

    // Default fallback for unknown terms
    console.log(`Using default fallback SNOMED code for unknown term: ${term}`);
    return { conceptId: '410942007', term: 'Drug or medicament' };
};