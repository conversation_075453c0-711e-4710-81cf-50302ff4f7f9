{"id": "0e42e339-e151-4bc9-bb4f-6388d3486909", "identifier": {"system": "http://hip.in", "value": "0e42e339-e151-4bc9-bb4f-6388d3486909"}, "meta": {"versionId": "1", "lastUpdated": "2025-03-11T10:41:18.216+05:30", "profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/DocumentBundle"], "security": [{"system": "http://terminology.hl7.org/CodeSystem/v3-Confidentiality", "code": "V", "display": "very restricted"}]}, "resourceType": "Bundle", "type": "document", "timestamp": "2025-03-11T10:41:18.216+05:30", "entry": [{"fullUrl": "urn:654306e9-aa20-49be-b2b1-f47b81e96b50", "resource": {"resourceType": "Composition", "status": "final", "type": {"coding": [{"system": "http://snomed.info/sct", "code": "371530004", "display": "Clinical consultation report"}], "text": "Clinical Consultation report"}, "subject": {"reference": "urn:67cfc2f84070ff5460cd0885", "display": "<PERSON><PERSON><PERSON>"}, "encounter": {"reference": "urn:cde113ee-4a67-4466-888a-bf64f44aefa5", "display": "Encounter"}, "date": "2025-03-11T10:41:18.216+05:30", "author": [{"reference": "urn:rk6484doc1132", "display": "Dr. <PERSON><PERSON><PERSON><PERSON>"}], "title": "Consultation Report", "custodian": {"reference": "urn:rk6484", "display": "R K Hospital"}, "section": [{"title": "Chief complaints", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "422843007", "display": "Chief complaint section"}]}, "entry": [{"reference": "urn:67cfc3094070ff5460cd0889", "display": "Condition"}, {"reference": "urn:67cfc30b4070ff5460cd088a", "display": "Condition"}, {"reference": "urn:67cfc30d4070ff5460cd088b", "display": "Condition"}, {"reference": "urn:67cfc30e4070ff5460cd088c", "display": "Condition"}]}, {"title": "Allergies", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "722446000", "display": "Allergy record"}]}, "entry": [{"reference": "urn:67cfc3334070ff5460cd0899", "display": "AllergyIntolerance"}, {"reference": "urn:67cfc3344070ff5460cd089a", "display": "AllergyIntolerance"}, {"reference": "urn:67cfc3384070ff5460cd089b", "display": "AllergyIntolerance"}]}, {"title": "FamilyHistory", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "422432008", "display": "Family history section"}]}, "entry": [{"reference": "urn:07609572-f0f5-4f0d-ae66-afc65b194588", "display": "FamilyMemberHistory"}, {"reference": "urn:57fb59f2-aeae-46bd-8a3d-c4c22cbda47f", "display": "FamilyMemberHistory"}, {"reference": "urn:a9264d3a-fdbd-435a-b453-c8ae0f308e42", "display": "FamilyMemberHistory"}]}, {"title": "Procedure", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "371525003", "display": "Clinical procedure report"}]}, "entry": [{"reference": "urn:67cfc374e576e131faa64ada", "display": "Procedure"}, {"reference": "urn:67cfc374e576e131faa64adb", "display": "Procedure"}, {"reference": "urn:67cfc374e576e131faa64adc", "display": "Procedure"}]}, {"title": "Follow Up", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "736271009", "display": "Outpatient care plan"}]}, "entry": [{"reference": "urn:67cfc3834070ff5460cd08a2", "display": "Appointment"}]}, {"title": "Other Observations", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "404684003", "display": "Clinical finding"}]}, "entry": [{"reference": "urn:67cfc3244070ff5460cd0894", "display": "Observation"}, {"reference": "urn:67cfc3154070ff5460cd088d", "display": "Observation"}, {"reference": "urn:67cfc3154070ff5460cd088f", "display": "Observation"}, {"reference": "urn:67cfc32c4070ff5460cd0897", "display": "Observation"}, {"reference": "urn:67cfc3154070ff5460cd088e", "display": "Observation"}, {"reference": "urn:67cfc3284070ff5460cd0896", "display": "Observation"}, {"reference": "urn:67cfc31d4070ff5460cd0891", "display": "Observation"}, {"reference": "urn:67cfc31d4070ff5460cd0892", "display": "Observation"}, {"reference": "urn:67cfc31d4070ff5460cd0893", "display": "Observation"}, {"reference": "urn:67cfc31d4070ff5460cd0890", "display": "Observation"}]}, {"title": "Medications", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "721912009", "display": "Medication summary document"}]}, "entry": [{"reference": "urn:67cfc3774070ff5460cd089f", "display": "MedicationStatement"}, {"reference": "urn:67cfc3774070ff5460cd08a0", "display": "MedicationStatement"}, {"reference": "urn:67cfc3774070ff5460cd08a1", "display": "MedicationStatement"}]}, {"title": "Op consultation Report", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "371530004", "display": "Clinical consultation report"}]}, "entry": [{"reference": "urn:1cfb44fc-4cec-45b6-97ed-2f7e523df9db", "display": "DocumentReference"}]}]}}, {"fullUrl": "urn:67cfc3094070ff5460cd0889", "resource": {"resourceType": "Condition", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "275926002", "display": "Screening - health check"}], "text": "Screening - health check"}, "subject": {"reference": "urn:67cfc2f84070ff5460cd0885", "type": "Patient", "display": "<PERSON><PERSON><PERSON>"}}}, {"fullUrl": "urn:67cfc30b4070ff5460cd088a", "resource": {"resourceType": "Condition", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "191991002", "display": "Tic NOS"}], "text": "Tic NOS"}, "subject": {"reference": "urn:67cfc2f84070ff5460cd0885", "type": "Patient", "display": "<PERSON><PERSON><PERSON>"}}}, {"fullUrl": "urn:67cfc30d4070ff5460cd088b", "resource": {"resourceType": "Condition", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "139490008", "display": "Headache"}], "text": "Headache"}, "subject": {"reference": "urn:67cfc2f84070ff5460cd0885", "type": "Patient", "display": "<PERSON><PERSON><PERSON>"}}}, {"fullUrl": "urn:67cfc30e4070ff5460cd088c", "resource": {"resourceType": "Condition", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "367206007", "display": "Pain"}], "text": "Pain"}, "subject": {"reference": "urn:67cfc2f84070ff5460cd0885", "type": "Patient", "display": "<PERSON><PERSON><PERSON>"}}}, {"fullUrl": "urn:67cfc3334070ff5460cd0899", "resource": {"resourceType": "AllergyIntolerance", "clinicalStatus": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/allergyintolerance-clinical", "code": "active", "display": "Active"}]}, "verificationStatus": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/allergyintolerance-verification", "code": "confirmed", "display": "Confirmed"}]}, "code": {"coding": [{"system": "http://snomed.info/sct", "code": "226793001", "display": "Soya milk"}], "text": "Soya milk"}, "patient": {"reference": "urn:67cfc2f84070ff5460cd0885", "type": "Patient", "display": "<PERSON><PERSON><PERSON>"}, "recordedDate": "2025-03-11T10:29:31.653+05:30", "recorder": {"reference": "urn:rk6484doc1132", "type": "Practitioner", "display": "Dr. INDRASENA REDDY"}}}, {"fullUrl": "urn:67cfc3344070ff5460cd089a", "resource": {"resourceType": "AllergyIntolerance", "clinicalStatus": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/allergyintolerance-clinical", "code": "active", "display": "Active"}]}, "verificationStatus": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/allergyintolerance-verification", "code": "confirmed", "display": "Confirmed"}]}, "code": {"coding": [{"system": "http://snomed.info/sct", "code": "102260001", "display": "Peanut butter"}], "text": "Peanut butter"}, "patient": {"reference": "urn:67cfc2f84070ff5460cd0885", "type": "Patient", "display": "<PERSON><PERSON><PERSON>"}, "recordedDate": "2025-03-11T10:29:32.803+05:30", "recorder": {"reference": "urn:rk6484doc1132", "type": "Practitioner", "display": "Dr. INDRASENA REDDY"}}}, {"fullUrl": "urn:67cfc3384070ff5460cd089b", "resource": {"resourceType": "AllergyIntolerance", "clinicalStatus": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/allergyintolerance-clinical", "code": "active", "display": "Active"}]}, "verificationStatus": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/allergyintolerance-verification", "code": "confirmed", "display": "Confirmed"}]}, "code": {"coding": [{"system": "http://snomed.info/sct", "code": "4925006", "display": "V-ABC protein"}], "text": "V-ABC protein"}, "patient": {"reference": "urn:67cfc2f84070ff5460cd0885", "type": "Patient", "display": "<PERSON><PERSON><PERSON>"}, "recordedDate": "2025-03-11T10:29:36.013+05:30", "recorder": {"reference": "urn:rk6484doc1132", "type": "Practitioner", "display": "Dr. INDRASENA REDDY"}}}, {"fullUrl": "urn:07609572-f0f5-4f0d-ae66-afc65b194588", "resource": {"resourceType": "FamilyMemberHistory", "patient": {"reference": "urn:67cfc2f84070ff5460cd0885", "type": "Patient", "display": "<PERSON><PERSON><PERSON>"}, "relationship": {"coding": [{"system": "http://snomed.info/sct", "code": "66839005", "display": "Father"}], "text": "Father"}, "status": "completed", "condition": [{"code": {"coding": [{"system": "http://snomed.info/sct", "code": "271646004", "display": "Low blood pressure"}], "text": "Low blood pressure"}}]}}, {"fullUrl": "urn:57fb59f2-aeae-46bd-8a3d-c4c22cbda47f", "resource": {"resourceType": "FamilyMemberHistory", "patient": {"reference": "urn:67cfc2f84070ff5460cd0885", "type": "Patient", "display": "<PERSON><PERSON><PERSON>"}, "relationship": {"coding": [{"system": "http://snomed.info/sct", "code": "66839005", "display": "Father"}], "text": "Father"}, "status": "completed", "condition": [{"code": {"coding": [{"system": "http://snomed.info/sct", "code": "277821009", "display": "Anxiety about having a fit"}], "text": "Anxiety about having a fit"}}]}}, {"fullUrl": "urn:a9264d3a-fdbd-435a-b453-c8ae0f308e42", "resource": {"resourceType": "FamilyMemberHistory", "patient": {"reference": "urn:67cfc2f84070ff5460cd0885", "type": "Patient", "display": "<PERSON><PERSON><PERSON>"}, "relationship": {"coding": [{"system": "http://snomed.info/sct", "code": "66839005", "display": "Father"}], "text": "Father"}, "status": "completed", "condition": [{"code": {"coding": [{"system": "http://snomed.info/sct", "code": "248542005", "display": "Diabetic relative"}], "text": "Diabetic relative"}}]}}, {"fullUrl": "urn:67cfc374e576e131faa64ada", "resource": {"resourceType": "Procedure", "status": "completed", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "408740001", "display": "Hip X-ray"}], "text": "Hip X-ray"}, "subject": {"reference": "urn:67cfc2f84070ff5460cd0885", "type": "Patient", "display": "<PERSON><PERSON><PERSON>"}, "recorder": {"reference": "urn:rk6484doc1132", "type": "Practitioner", "display": "Dr. INDRASENA REDDY"}, "performer": [{"actor": {"reference": "urn:rk6484doc1132", "type": "Practitioner", "display": "Dr. INDRASENA REDDY"}, "onBehalfOf": {"reference": "urn:rk6484", "type": "Organization", "display": "R K Hospital"}}]}}, {"fullUrl": "urn:67cfc374e576e131faa64adb", "resource": {"resourceType": "Procedure", "status": "completed", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "446063006", "display": "MRI of rib"}], "text": "MRI of rib"}, "subject": {"reference": "urn:67cfc2f84070ff5460cd0885", "type": "Patient", "display": "<PERSON><PERSON><PERSON>"}, "recorder": {"reference": "urn:rk6484doc1132", "type": "Practitioner", "display": "Dr. INDRASENA REDDY"}, "performer": [{"actor": {"reference": "urn:rk6484doc1132", "type": "Practitioner", "display": "Dr. INDRASENA REDDY"}, "onBehalfOf": {"reference": "urn:rk6484", "type": "Organization", "display": "R K Hospital"}}]}}, {"fullUrl": "urn:67cfc374e576e131faa64adc", "resource": {"resourceType": "Procedure", "status": "completed", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "396550006", "display": "Blood test"}], "text": "Blood test"}, "subject": {"reference": "urn:67cfc2f84070ff5460cd0885", "type": "Patient", "display": "<PERSON><PERSON><PERSON>"}, "recorder": {"reference": "urn:rk6484doc1132", "type": "Practitioner", "display": "Dr. INDRASENA REDDY"}, "performer": [{"actor": {"reference": "urn:rk6484doc1132", "type": "Practitioner", "display": "Dr. INDRASENA REDDY"}, "onBehalfOf": {"reference": "urn:rk6484", "type": "Organization", "display": "R K Hospital"}}]}}, {"fullUrl": "urn:67cfc3834070ff5460cd08a2", "resource": {"resourceType": "Appointment", "status": "pending", "reasonCode": [{"coding": [{"system": "http://snomed.info/sct", "code": "359649009", "display": "Reducing diet"}], "text": "Reducing diet"}], "priority": 0, "supportingInformation": [{"reference": "urn:67cfc3084070ff5460cd0888", "type": "Appointment"}, {"reference": "urn:rk6484", "type": "Organization", "display": "R K Hospital"}], "start": "2025-03-17T09:00:00Z", "end": "2025-03-17T09:30:00Z", "minutesDuration": 30, "created": "2025-03-11T10:30:51.192+05:30", "participant": [{"actor": {"reference": "urn:rk6484doc1132", "type": "Practitioner", "display": "Dr. INDRASENA REDDY"}, "required": "required", "status": "accepted"}, {"actor": {"reference": "urn:67cfc2f84070ff5460cd0885", "type": "Patient", "display": "<PERSON><PERSON><PERSON>"}, "required": "required", "status": "accepted"}]}}, {"fullUrl": "urn:67cfc3244070ff5460cd0894", "resource": {"resourceType": "Observation", "status": "final", "category": [{"coding": [{"system": "http://terminology.hl7.org/CodeSystem/observation-category", "code": "vital-signs", "display": "Vital Signs"}], "text": "Vital Signs"}], "code": {"coding": [{"system": "http://loinc.org", "code": "85354-9", "display": "Blood pressure panel with all children optional"}], "text": "Blood pressure panel with all children optional"}, "subject": {"reference": "urn:67cfc2f84070ff5460cd0885", "type": "Patient", "display": "<PERSON><PERSON><PERSON>"}, "effectiveDateTime": "2025-03-11T04:59:14.480Z", "issued": "2025-03-11T10:29:16.275+05:30", "performer": [{"reference": "urn:rk6484doc1132", "type": "Practitioner", "display": "Dr. INDRASENA REDDY"}, {"reference": "urn:rk6484", "type": "Organization", "display": "R K Hospital"}], "component": [{"code": {"coding": [{"system": "http://loinc.org", "code": "8462-4", "display": "Diastolic blood pressure"}], "text": "Diastolic blood pressure"}, "valueQuantity": {"value": 120.0, "unit": "mmhg", "system": "http://unitsofmeasure.org", "code": "mm[Hg]"}}, {"code": {"coding": [{"system": "http://loinc.org", "code": "8480-6", "display": "Systolic blood pressure"}], "text": "Systolic blood pressure"}, "valueQuantity": {"value": 80.0, "unit": "mmhg", "system": "http://unitsofmeasure.org", "code": "mm[Hg]"}}]}}, {"fullUrl": "urn:67cfc3154070ff5460cd088d", "resource": {"resourceType": "Observation", "status": "final", "category": [{"coding": [{"system": "http://terminology.hl7.org/CodeSystem/observation-category", "code": "vital-signs", "display": "Vital Signs"}], "text": "Vital Signs"}], "code": {"coding": [{"system": "http://loinc.org", "code": "8302-2", "display": "Body height"}], "text": "Body height"}, "subject": {"reference": "urn:67cfc2f84070ff5460cd0885", "type": "Patient", "display": "<PERSON><PERSON><PERSON>"}, "effectiveDateTime": "2025-03-11T05:11:18Z", "issued": "2025-03-11T10:29:01.025+05:30", "performer": [{"reference": "urn:rk6484doc1132", "type": "Practitioner", "display": "Dr. INDRASENA REDDY"}, {"reference": "urn:rk6484", "type": "Organization", "display": "R K Hospital"}], "valueQuantity": {"value": 196.0, "unit": "cm", "system": "http://unitsofmeasure.org", "code": "cm"}}}, {"fullUrl": "urn:67cfc3154070ff5460cd088f", "resource": {"resourceType": "Observation", "status": "final", "category": [{"coding": [{"system": "http://terminology.hl7.org/CodeSystem/observation-category", "code": "vital-signs", "display": "Vital Signs"}], "text": "Vital Signs"}], "code": {"coding": [{"system": "http://snomed.info/sct", "code": "446974000", "display": "BMI (body mass index) centile"}], "text": "BMI"}, "subject": {"reference": "urn:67cfc2f84070ff5460cd0885", "type": "Patient", "display": "<PERSON><PERSON><PERSON>"}, "effectiveDateTime": "2025-03-11T05:11:18Z", "issued": "2025-03-11T10:29:01.025+05:30", "performer": [{"reference": "urn:rk6484doc1132", "type": "Practitioner", "display": "Dr. INDRASENA REDDY"}, {"reference": "urn:rk6484", "type": "Organization", "display": "R K Hospital"}], "valueQuantity": {"value": 20.82, "unit": "kg/m2", "system": "http://unitsofmeasure.org", "code": "kg/m2"}}}, {"fullUrl": "urn:67cfc32c4070ff5460cd0897", "resource": {"resourceType": "Observation", "status": "final", "category": [{"coding": [{"system": "http://terminology.hl7.org/CodeSystem/observation-category", "code": "vital-signs", "display": "Vital Signs"}], "text": "Vital Signs"}], "code": {"coding": [{"system": "http://snomed.info/sct", "code": "226346006", "display": "Milk sugar intake"}], "text": "Milk sugar intake"}, "subject": {"reference": "urn:67cfc2f84070ff5460cd0885", "type": "Patient", "display": "<PERSON><PERSON><PERSON>"}, "effectiveDateTime": "2025-03-11T05:11:18Z", "issued": "2025-03-11T10:29:24.979+05:30", "performer": [{"reference": "urn:rk6484doc1132", "type": "Practitioner", "display": "Dr. INDRASENA REDDY"}, {"reference": "urn:rk6484", "type": "Organization", "display": "R K Hospital"}], "valueQuantity": {"value": 123.0, "system": "http://unitsofmeasure.org"}}}, {"fullUrl": "urn:67cfc3154070ff5460cd088e", "resource": {"resourceType": "Observation", "status": "final", "category": [{"coding": [{"system": "http://terminology.hl7.org/CodeSystem/observation-category", "code": "vital-signs", "display": "Vital Signs"}], "text": "Vital Signs"}], "code": {"coding": [{"system": "http://snomed.info/sct", "code": "27113001", "display": "Body weight"}], "text": "Body weight"}, "subject": {"reference": "urn:67cfc2f84070ff5460cd0885", "type": "Patient", "display": "<PERSON><PERSON><PERSON>"}, "effectiveDateTime": "2025-03-11T05:11:18Z", "issued": "2025-03-11T10:29:01.025+05:30", "performer": [{"reference": "urn:rk6484doc1132", "type": "Practitioner", "display": "Dr. INDRASENA REDDY"}, {"reference": "urn:rk6484", "type": "Organization", "display": "R K Hospital"}], "valueQuantity": {"value": 80.0, "unit": "kg", "system": "http://unitsofmeasure.org", "code": "kg"}}}, {"fullUrl": "urn:67cfc3284070ff5460cd0896", "resource": {"resourceType": "Observation", "status": "final", "category": [{"coding": [{"system": "http://terminology.hl7.org/CodeSystem/observation-category", "code": "vital-signs", "display": "Vital Signs"}], "text": "Vital Signs"}], "code": {"coding": [{"system": "http://snomed.info/sct", "code": "269864005", "display": "Blood glucose result"}], "text": "Blood glucose result"}, "subject": {"reference": "urn:67cfc2f84070ff5460cd0885", "type": "Patient", "display": "<PERSON><PERSON><PERSON>"}, "effectiveDateTime": "2025-03-11T05:11:18Z", "issued": "2025-03-11T10:29:20.794+05:30", "performer": [{"reference": "urn:rk6484doc1132", "type": "Practitioner", "display": "Dr. INDRASENA REDDY"}, {"reference": "urn:rk6484", "type": "Organization", "display": "R K Hospital"}], "valueQuantity": {"value": 21.0, "system": "http://unitsofmeasure.org"}}}, {"fullUrl": "urn:67cfc31d4070ff5460cd0891", "resource": {"resourceType": "Observation", "status": "final", "category": [{"coding": [{"system": "http://terminology.hl7.org/CodeSystem/observation-category", "code": "vital-signs", "display": "Vital Signs"}], "text": "Vital Signs"}], "code": {"coding": [{"system": "http://snomed.info/sct", "code": "250554003", "display": "Measurement of oxygen saturation at periphery"}], "text": "Measurement of oxygen saturation at periphery"}, "subject": {"reference": "urn:67cfc2f84070ff5460cd0885", "type": "Patient", "display": "<PERSON><PERSON><PERSON>"}, "effectiveDateTime": "2025-03-11T04:59:09.109Z", "issued": "2025-03-11T10:29:09.856+05:30", "performer": [{"reference": "urn:rk6484doc1132", "type": "Practitioner", "display": "Dr. INDRASENA REDDY"}, {"reference": "urn:rk6484", "type": "Organization", "display": "R K Hospital"}], "valueQuantity": {"value": 98.0, "unit": "%", "system": "http://unitsofmeasure.org", "code": "%"}}}, {"fullUrl": "urn:67cfc31d4070ff5460cd0892", "resource": {"resourceType": "Observation", "status": "final", "category": [{"coding": [{"system": "http://terminology.hl7.org/CodeSystem/observation-category", "code": "vital-signs", "display": "Vital Signs"}], "text": "Vital Signs"}], "code": {"coding": [{"system": "http://loinc.org", "code": "61008-9", "display": "Body surface temperature"}], "text": "Body surface temperature"}, "subject": {"reference": "urn:67cfc2f84070ff5460cd0885", "type": "Patient", "display": "<PERSON><PERSON><PERSON>"}, "effectiveDateTime": "2025-03-11T04:59:09.109Z", "issued": "2025-03-11T10:29:09.856+05:30", "performer": [{"reference": "urn:rk6484doc1132", "type": "Practitioner", "display": "Dr. INDRASENA REDDY"}, {"reference": "urn:rk6484", "type": "Organization", "display": "R K Hospital"}], "valueQuantity": {"value": 98.0, "unit": "degF", "system": "http://unitsofmeasure.org", "code": "[degF]"}}}, {"fullUrl": "urn:67cfc31d4070ff5460cd0893", "resource": {"resourceType": "Observation", "status": "final", "category": [{"coding": [{"system": "http://terminology.hl7.org/CodeSystem/observation-category", "code": "vital-signs", "display": "Vital Signs"}], "text": "Vital Signs"}], "code": {"coding": [{"system": "http://snomed.info/sct", "code": "86290005", "display": "Respiratory rate"}], "text": "Respiratory rate"}, "subject": {"reference": "urn:67cfc2f84070ff5460cd0885", "type": "Patient", "display": "<PERSON><PERSON><PERSON>"}, "effectiveDateTime": "2025-03-11T04:59:09.109Z", "issued": "2025-03-11T10:29:09.856+05:30", "performer": [{"reference": "urn:rk6484doc1132", "type": "Practitioner", "display": "Dr. INDRASENA REDDY"}, {"reference": "urn:rk6484", "type": "Organization", "display": "R K Hospital"}], "valueQuantity": {"value": 73.0, "unit": "beats/min", "system": "http://unitsofmeasure.org", "code": "/min"}}}, {"fullUrl": "urn:67cfc31d4070ff5460cd0890", "resource": {"resourceType": "Observation", "status": "final", "category": [{"coding": [{"system": "http://terminology.hl7.org/CodeSystem/observation-category", "code": "vital-signs", "display": "Vital Signs"}], "text": "Vital Signs"}], "code": {"coding": [{"system": "http://snomed.info/sct", "code": "78564009", "display": "Pulse rate"}], "text": "Pulse rate"}, "subject": {"reference": "urn:67cfc2f84070ff5460cd0885", "type": "Patient", "display": "<PERSON><PERSON><PERSON>"}, "effectiveDateTime": "2025-03-11T04:59:09.109Z", "issued": "2025-03-11T10:29:09.856+05:30", "performer": [{"reference": "urn:rk6484doc1132", "type": "Practitioner", "display": "Dr. INDRASENA REDDY"}, {"reference": "urn:rk6484", "type": "Organization", "display": "R K Hospital"}], "valueQuantity": {"value": 72.0, "unit": "beats/min", "system": "http://unitsofmeasure.org", "code": "/min"}}}, {"fullUrl": "urn:67cfc3774070ff5460cd089f", "resource": {"resourceType": "MedicationStatement", "status": "completed", "medicationCodeableConcept": {"coding": [{"system": "http://snomed.info/sct", "code": "322236009", "display": "Paracetamol 500mg tablet"}], "text": "Paracetamol 500mg tablet"}, "subject": {"reference": "urn:67cfc2f84070ff5460cd0885", "type": "Patient", "display": "<PERSON><PERSON><PERSON>"}, "informationSource": {"reference": "urn:rk6484doc1132", "type": "Practitioner", "display": "Dr. INDRASENA REDDY"}, "derivedFrom": [{"reference": "urn:rk6484", "type": "Organization", "display": "R K Hospital"}], "dateAsserted": "2025-03-11T10:41:18.215+05:30", "dosage": [{"additionalInstruction": [{"coding": [{"system": "http://snomed.info/sct", "code": "311504000", "display": "With or after food"}], "text": "After Food"}], "timing": {"code": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/v3-GTSAbbreviation", "code": "BID", "display": "BID"}], "text": "1-0-0-1"}, "repeat": {"frequency": 1, "period": 1, "periodUnit": "d"}}}]}}, {"fullUrl": "urn:67cfc3774070ff5460cd08a0", "resource": {"resourceType": "MedicationStatement", "status": "completed", "medicationCodeableConcept": {"coding": [{"system": "http://snomed.info/sct", "code": "408072005", "display": "Dolasetron 148mg tablet"}], "text": "Dolasetron 148mg tablet"}, "subject": {"reference": "urn:67cfc2f84070ff5460cd0885", "type": "Patient", "display": "<PERSON><PERSON><PERSON>"}, "informationSource": {"reference": "urn:rk6484doc1132", "type": "Practitioner", "display": "Dr. INDRASENA REDDY"}, "derivedFrom": [{"reference": "urn:rk6484", "type": "Organization", "display": "R K Hospital"}], "dateAsserted": "2025-03-11T10:41:18.215+05:30", "dosage": [{"additionalInstruction": [{"coding": [{"system": "http://snomed.info/sct", "code": "311504000", "display": "With or after food"}], "text": "After Food"}], "timing": {"code": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/v3-GTSAbbreviation", "code": "TID", "display": "TID"}], "text": "1-1-0-1"}, "repeat": {"frequency": 1, "period": 1, "periodUnit": "d"}}}]}}, {"fullUrl": "urn:67cfc3774070ff5460cd08a1", "resource": {"resourceType": "MedicationStatement", "status": "completed", "medicationCodeableConcept": {"coding": [{"system": "http://snomed.info/sct", "code": "329525004", "display": "Aspirin 300mg tablet"}], "text": "Aspirin 300mg tablet"}, "subject": {"reference": "urn:67cfc2f84070ff5460cd0885", "type": "Patient", "display": "<PERSON><PERSON><PERSON>"}, "informationSource": {"reference": "urn:rk6484doc1132", "type": "Practitioner", "display": "Dr. INDRASENA REDDY"}, "derivedFrom": [{"reference": "urn:rk6484", "type": "Organization", "display": "R K Hospital"}], "dateAsserted": "2025-03-11T10:41:18.215+05:30", "dosage": [{"additionalInstruction": [{"coding": [{"system": "http://snomed.info/sct", "code": "311504000", "display": "With or after food"}], "text": "After Food"}], "timing": {"code": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/v3-GTSAbbreviation", "code": "BID", "display": "BID"}], "text": "1-0-0-1"}, "repeat": {"frequency": 1, "period": 1, "periodUnit": "d"}}}]}}, {"fullUrl": "urn:1cfb44fc-4cec-45b6-97ed-2f7e523df9db", "resource": {"resourceType": "DocumentReference", "id": "1cfb44fc-4cec-45b6-97ed-2f7e523df9db", "status": "current", "docStatus": "final", "type": {"coding": [{"system": "http://snomed.info/sct", "code": "229059009", "display": "Report"}], "text": "Report"}, "subject": {"reference": "urn:67cfc2f84070ff5460cd0885", "display": "Patient"}, "content": [{"attachment": {"contentType": "application/pdf", "language": "en-IN", "data": "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", "title": "OP consultation Report"}}]}}, {"fullUrl": "urn:rk6484doc1132", "resource": {"resourceType": "Practitioner", "identifier": [{"use": "official", "type": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/v2-0203", "code": "MR", "display": "Medical record number"}], "text": "hprId"}, "value": "21-1521-3828-3227"}], "name": [{"use": "official", "text": "Dr. <PERSON><PERSON><PERSON><PERSON>", "prefix": ["Dr."]}], "birthDate": "2002-01-02", "telecom": [{"system": "phone", "value": "**********", "use": "work"}, {"system": "email", "value": "<EMAIL>", "use": "work"}], "gender": "male"}}, {"fullUrl": "urn:67cfc2f84070ff5460cd0885", "resource": {"resourceType": "Patient", "identifier": [{"system": "https://healthid.ndhm.gov.in", "type": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/v2-0203", "code": "MR", "display": "Medical record number"}], "text": "ABHAID"}, "value": "91-3074-8744-6345"}, {"system": "https://healthid.ndhm.gov.in", "type": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/v2-0203", "code": "MR", "display": "Medical record number"}], "text": "ABHAADDRESS"}, "value": "himanshugoel0212@sbx"}], "name": [{"use": "official", "text": "<PERSON><PERSON><PERSON>", "prefix": ["Mr"]}], "birthDate": "1998-12-02", "telecom": [{"system": "phone", "value": "**********"}], "gender": "male"}}, {"fullUrl": "urn:cde113ee-4a67-4466-888a-bf64f44aefa5", "resource": {"resourceType": "Encounter", "class": {"system": "http://terminology.hl7.org/CodeSystem/v3-ActCode", "display": "ambulatory", "code": "AMB"}, "status": "finished", "appointment": [{"reference": "urn:67cfc3084070ff5460cd0888", "display": "Appointment"}]}}, {"fullUrl": "urn:67cfc3084070ff5460cd0888", "resource": {"resourceType": "Appointment", "status": "fulfilled", "specialty": [{"coding": [{"system": "http://snomed.info/sct", "code": "394579002", "display": "Cardiology"}]}], "appointmentType": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/v2-0276", "version": "2.9", "code": "WALKIN", "display": "A previously unscheduled walk-in visit"}], "text": "WALKIN"}, "reasonCode": [{"coding": [{"system": "http://snomed.info/sct", "code": "275926002", "display": "Screening - health check"}], "text": "Screening - health check"}], "supportingInformation": [{"reference": "urn:rk6484", "type": "Organization", "display": "R K Hospital"}], "start": "2025-03-11T10:28:00Z", "end": "2025-03-11T10:38:00Z", "minutesDuration": 10, "participant": [{"actor": {"reference": "urn:rk6484doc1132", "type": "Practitioner", "display": "Dr. INDRASENA REDDY"}, "required": "required", "status": "accepted"}, {"actor": {"reference": "urn:67cfc2f84070ff5460cd0885", "type": "Patient", "display": "<PERSON><PERSON><PERSON>"}, "required": "required", "status": "accepted"}]}}, {"fullUrl": "urn:rk6484", "resource": {"resourceType": "Organization", "identifier": [{"use": "official", "type": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/v2-0203", "code": "MR", "display": "Medical record number"}], "text": "hfrId"}, "value": "IN2810001578"}, {"use": "official", "type": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/v2-0203", "code": "MR", "display": "Medical record number"}], "text": "hprId"}}], "active": true, "type": [{"coding": [{"system": "http://terminology.hl7.org/CodeSystem/organization-type", "version": "4.0.1", "code": "prov", "display": "Healthcare Provider"}], "text": "Healthcare Provider"}], "name": "R K Hospital", "telecom": [{"system": "phone", "value": "**********", "use": "work"}, {"system": "email", "value": "<EMAIL>", "use": "work"}], "address": [{"use": "work", "text": "J279+4X Maddipadu, Andhra Pradesh, India", "city": "<PERSON><PERSON><PERSON><PERSON>", "state": "Andhra Pradesh", "postalCode": "523211", "country": "India", "type": "postal"}]}}]}