/**
 * ABDM HIU Data Transfer Format Example
 * 
 * This file demonstrates how to use the formatForABDMTransfer function
 * to properly format FHIR bundles for ABDM HIU API compliance.
 * 
 * Error Fixed: ABDM-7721 "Invalid data transfer request with missing entries information"
 */

import { formatForABDMTransfer } from './main.bundle.fhir.helper.js';

/**
 * Example usage of formatForABDMTransfer function
 * 
 * @param {Object} fhirBundle - The generated FHIR bundle
 * @param {string} transactionId - ABDM transaction ID
 * @param {string} hipId - Health Information Provider ID
 * @returns {Object} ABDM-compliant data transfer format
 */
export const exampleABDMTransfer = (fhirBundle, transactionId, hipId) => {
  // Format the FHIR bundle for ABDM HIU API
  const abdmFormattedData = formatForABDMTransfer(fhirBundle, transactionId, hipId);
  
  console.log('ABDM Formatted Data:', JSON.stringify(abdmFormattedData, null, 2));
  
  return abdmFormattedData;
};

/**
 * Example of how to use this in your HIU data transfer endpoint
 */
export const exampleHIUEndpoint = async (req, res) => {
  try {
    // 1. Generate your FHIR bundle (existing code)
    const fhirBundle = await generateYourFhirBundle(req.body);
    
    // 2. Extract ABDM parameters from request
    const transactionId = req.body.transactionId || req.headers['transaction-id'];
    const hipId = req.body.hipId || process.env.HIP_ID;
    
    // 3. Format for ABDM transfer
    const abdmData = formatForABDMTransfer(fhirBundle, transactionId, hipId);
    
    // 4. Send to ABDM HIU API
    const response = await sendToABDMHIU(abdmData);
    
    res.json({ success: true, response });
  } catch (error) {
    console.error('ABDM Transfer Error:', error);
    res.status(500).json({ error: error.message });
  }
};

/**
 * Mock function - replace with your actual FHIR bundle generation
 */
async function generateYourFhirBundle(data) {
  // Your existing FHIR bundle generation logic
  return {
    resourceType: "Bundle",
    id: "example-bundle",
    type: "document",
    entry: [
      // Your FHIR resources
    ]
  };
}

/**
 * Mock function - replace with your actual ABDM HIU API call
 */
async function sendToABDMHIU(abdmData) {
  // Your ABDM HIU API call logic
  const response = await fetch('https://abhasbx.abdm.gov.in/abha/api/v3/patient-hiu/app/v0.5/health-information/transfer', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      // Add your ABDM authentication headers
    },
    body: JSON.stringify(abdmData)
  });
  
  return response.json();
}
