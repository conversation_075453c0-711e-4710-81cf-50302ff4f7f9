import { v4 as uuidv4 } from "uuid";
import {
  basePriceComponent,
  discountPriceComponent,
  informationPriceComponent,
  invoiceCodeConstant,
  invoiceMetaData,
  taxCGSTPriceComponent,
  taxSGSTPriceComponent,
  taxIGSTPriceComponent,
} from "../../../utils/fhir.constants.js";

export const generateInvoiceResource = async (
  invoice,
  patientResource,
  practitionerResources,
  organizationResource,
  chargeItemResources,
  currentTime
) => {
  const id = uuidv4();

  return {
    fullUrl: `urn:uuid:${id}`,
    resource: {
      resourceType: "Invoice",
      id,
      meta: invoiceMetaData(currentTime),
      text: {
        status: "generated",
        div: `<div xmlns="http://www.w3.org/1999/xhtml"><p><b>Generated Narrative: Invoice</b></p><div style="display: inline-block; background-color: #d9e0e7; padding: 6px; margin: 4px; border: 1px solid #8da1b4; border-radius: 5px; line-height: 60%"><p style="margin-bottom: 0px">Resource Invoice &quot;${id}&quot;</p></div><p><b>status</b>: issued</p><p><b>type</b>: Invoice <span style="background: LightGoldenRodYellow; margin: 4px; border: 1px solid khaki"> (<a href="CodeSystem-ndhm-invoice-type.html">NDHM Invoice Type</a>#invoice)</span></p><p><b>subject</b>: <a href="#Patient_example-01">See above (Patient/example-01)</a></p><p><b>date</b>: ${invoice.date}</p><p><b>totalNet</b>: ${invoice.totalNet.value} ${invoice.totalNet.currency}</p><p><b>totalGross</b>: ${invoice.totalGross.value} ${invoice.totalGross.currency}</p></div>`
      },
      status: "issued",
      type: invoiceCodeConstant(),
      date: invoice.date,
      identifier: [
        {
          value: invoice.id,
        },
      ],
      totalNet: invoice.totalNet,
      totalGross: invoice.totalGross,
      subject: {
        reference: patientResource.fullUrl,
        display: "Patient",
      },

      lineItem: invoice.lineItem.map((Item, index) => {
        const matchingChargeItem = chargeItemResources.find((resource) => {
          console.log(
            "Found matching chargeItemResource",
            resource.resource.productCodeableConcept,
            Item
          );
          return (
            resource.resource.productCodeableConcept.text?.toLowerCase() ===
            Item.type?.toLowerCase()
          );
        });

        return {
          sequence: index + 1,
          chargeItemReference: matchingChargeItem
            ? {
                reference: `${matchingChargeItem.fullUrl}`,
                display: "Charge Item",
              }
            : undefined,
          priceComponent: Item.priceComponent?.map((component) => ({
            type: component.type === "information" ? "informational" : component.type,
            code:
              component.type === "base"
                ? basePriceComponent()
                : component.type === "information"
                ? informationPriceComponent()
                : component.type === "discount"
                ? discountPriceComponent()
                : component.type === "tax" && component.display === "CGST"
                ? taxCGSTPriceComponent()
                : component.type === "tax" && component.display === "SGST"
                ? taxSGSTPriceComponent()
                : component.type === "tax" && component.display === "IGST"
                ? taxIGSTPriceComponent()
                : {},
            amount: component.amount,
          })),
        };
      }),
      participant: practitionerResources.map((practitioner) => ({
        actor: { reference: practitioner.fullUrl, display: "Practitioner" },
      })),
      issuer: { reference: organizationResource.fullUrl },
    },
  };
};
