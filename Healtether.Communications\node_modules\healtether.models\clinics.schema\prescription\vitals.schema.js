import mongoose from "mongoose";
import { CLIENT_COLLECTION, PATIENT_COLLECTION, QUEUED_APPOINTMENT_COLLECTION } from "../../mongodb.collection.name.js";

const vitalsSchema = new mongoose.Schema(
  {
    bloodPressure: {
      systolic: {
        type: Number,
        displayName: "Systolic blood pressure",
        displayCode: "8480-6",
        unit: "mmHg",
        unitCode: "mm[Hg]"
      },
      diastolic: {
        type: Number,
        displayName: "Diastolic blood pressure",
        displayCode: "8462-4",
        unit: "mmHg",
        unitCode: "mm[Hg]"
      }
    },
    spo2: {
      type: Number,
      displayName: "Oxygen saturation in Arterial blood",
      displayCode: "2710-2",
      unit: "%",
      unitCode: "%"
    },
    temperature: {
      type: Number,
      displayName: "Body temperature",
      displayCode: "8310-5",
      unit: "°F",
      unitCode: "[degF]"
    },
    height: {
      type: Number,
      displayName: "Body height",
      displayCode: "8302-2",
      unit: "cm",
      unitCode: "cm"
    },
    weight: {
      type: Number,
      displayName: "Body weight",
      displayCode: "29463-7",
      unit: "kg",
      unitCode: "kg"
    },
    pulseRate: {
      type: Number,
      displayName: "Pulse rate",
      displayCode: "8867-4",
      unit: "beats/min",
      unitCode: "/min"
    },
    rbs: {
      type: Number,
      displayName: "Random blood sugar",
      displayCode: "14743-9",
      unit: "mg/dL",
      unitCode: "mg/dL"
    },
    heartRate: {
      type: Number,
      displayName: "Heart rate",
      displayCode: "8867-4",
      unit: "beats/min",
      unitCode: "/min"
    },
    respiratoryRate: {
      type: Number,
      displayName: "Respiratory rate",
      displayCode: "9279-1",
      unit: "breaths/min",
      unitCode: "/min"
    },
    interpretation: {
      type: String,
      displayName: "Clinical interpretation of vital signs",
      maxLength: 255
    },
    appointment: {
      type: mongoose.Schema.Types.ObjectId,
      ref: QUEUED_APPOINTMENT_COLLECTION
    },
    patient: {
      type: mongoose.Schema.Types.ObjectId,
      ref: PATIENT_COLLECTION
    },
    deleted: {
      type: Boolean,
      default: false
    },
    clinic: {
      type: mongoose.Schema.Types.ObjectId,
      ref: CLIENT_COLLECTION
    },
    created: {
      on: {
        type: Date,
        default: Date.now
      },
      by: {
        id: String,
        name: {
          type: String,
          maxLength: 255
        }
      }
    },
    modified: {
      on: {
        type: Date
      },
      by: {
        id: String,
        name: {
          type: String,
          maxLength: 255
        }
      }
    }
  },
  { versionKey: "2.0", timestamps: true }
);

vitalsSchema.index({ clinic: 1, patient: 1, appointment: 1 });

export { vitalsSchema };
