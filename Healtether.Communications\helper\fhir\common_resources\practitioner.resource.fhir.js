import { v4 as uuidv4 } from "uuid";
import { practitionerMetadata, practitionerIdentifiers, practitionerDiv } from "../../../utils/fhir.constants.js";
import { toTitleCase } from "../../../utils/titlecase.generator.js";

export const generatePractitionerResource = async (currentTime, practitioner, doctors) => {
    const normalize = (str) => str.trim().toLowerCase();
    const normalizedDoctors = doctors.map(normalize);
console.log("practitioner",practitioner);
    const hasMatchingDoctor = practitioner.names.some(name => normalizedDoctors.includes(normalize(name)));
    if (!hasMatchingDoctor) return null;

    const id = uuidv4();

    const getAddressWithoutId = (patient) => {
        if (patient.address && patient.address.length > 0) {
          const { _id, ...addressWithoutId } = patient.address[0];
          return addressWithoutId;
        }
        return null;
      };
    return {
        fullUrl: `urn:uuid:${id}`,
        resource: {
            resourceType: 'Practitioner',
            id,
            meta: practitionerMetadata(currentTime),
            identifier: practitionerIdentifiers(practitioner.licenses),
            name: practitioner.names.map(name => ({use:"official", text: toTitleCase(name),prefix:["Dr."]})),
            telecom: practitioner.telecom.map(telecom => ({
                system: telecom.system,
                value: telecom.value,
                use: telecom.use
            })),
            gender: practitioner.gender,
            birthDate: practitioner.dob,
            address: getAddressWithoutId(practitioner) ? [getAddressWithoutId(practitioner)] : []
            // text: practitionerDiv()
        }
    };
};