
import { v4 as uuidv4 } from "uuid";
import { medicationRequestMetadata, medicationRequestDiv } from "../../../utils/fhir.constants.js";
import { toTitleCase } from "../../../utils/titlecase.generator.js";
import { getSnomedCtCode } from "../../../utils/fhir.constants.js";
import { generateSnomedCtCode } from "./snomed_ct_code.generator.fhir.js";
import portalDb from "../../../config/clinics.collections.config.js";
const Prescription = portalDb.model("Prescription");

export const generateMedicationRequestResource = async (
    status, intent, authoredOn, medication, forCondition, forReasons, dosageInstruction,
    patientResource, practitionerResources, conditionResources, doctors, patientId
) => {
    const requestId = uuidv4();
    console.log("forReasons", forReasons);
    const prescription = await Prescription.findOne({ patient: patientId }).sort({
        "created.on": -1,
    });

    const normalize = (str) => str.trim().toLowerCase();
    const normalizedDoctors = doctors.map(normalize);

    const matchingPractitioner = practitionerResources.find(practitioner =>
        practitioner.resource.name.some(nameObj => normalizedDoctors.includes(normalize(nameObj.text)))
    );

    const getSnomedDataMedication = await generateSnomedCtCode(medication);

    // Process forReasons as an array asynchronously
    let getSnomedDataMedicationReasons = [];
    if (forReasons && forReasons.length > 0) {
        getSnomedDataMedicationReasons = await Promise.all(
            forReasons.map(async (reason) => {
                const snomedData = await generateSnomedCtCode(reason);
                const duration = prescription.symptoms
                    .filter((sym) => sym.name === snomedData.term)
                    .map((diag) => `${diag.duration.value} ${diag.duration.unit}`)
                    .join(" ");
                const notes = `(Duration: ${duration}) (Notes: ${prescription.symptoms
                    .filter((sym) => sym.name === snomedData.term)
                    .map((d) => d.notes)
                    .join(" ")})`;

                return getSnomedCtCode(
                    snomedData.conceptId,
                    toTitleCase(snomedData.term),
                    notes
                );
            })
        );
    }

    // Process forCondition as an array
    let matchedConditions = [];
    if (forCondition && forCondition.length > 0) {
        matchedConditions = forCondition.map(condition => {
            const normalizedCondition = condition.trim().toLowerCase();
            return conditionResources.find(cond =>
                cond.resource.code.coding.some(coding => coding.display.trim().toLowerCase() === normalizedCondition)
            );
        }).filter(Boolean); // Remove any undefined values
    }

    let finalDosageInstructions = [];

    for (const instruction of dosageInstruction) {
        let dosageInstructions = {};

        for (const [key, value] of Object.entries(instruction)) {
            if (key === "text") {
                dosageInstructions.text = value || "Take as directed";
            } else if (key === "repeat" && typeof value === "object") {
                // Fix timing repeat values for FHIR compliance
                const fixedRepeat = { ...value };

                // Fix periodUnit: convert "Days" to "d" for FHIR compliance
                if (fixedRepeat.periodUnit === "Days") {
                    fixedRepeat.periodUnit = "d";
                }

                // Fix frequency: ensure it's a valid integer
                if (typeof fixedRepeat.frequency === "string") {
                    // Convert string like "0-1-1" to a valid integer
                    const freqMatch = fixedRepeat.frequency.match(/\d+/);
                    fixedRepeat.frequency = freqMatch ? parseInt(freqMatch[0]) || 1 : 1;
                }

                dosageInstructions.timing = { repeat: fixedRepeat };
            } else if (key === "additionalInstruction" && typeof value === "string" && value) {
                console.log(`additionalInstruction: ${value}`);
                try {
                    const additionalInstructionData = await generateSnomedCtCode(value);
                    dosageInstructions.additionalInstruction = [
                        getSnomedCtCode(additionalInstructionData.conceptId, additionalInstructionData.term)
                    ];
                }
                catch (error) {
                    try {
                        console.log("using fallbacks for additional instructions");
                        // Add SNOMED coded additional instruction
                        // Map common additional instructions to SNOMED codes
                        const getAdditionalInstructionCode = (instruction) => {
                            const instructionMap = {
                                'with food': { system: "http://snomed.info/sct", code: "311504000", display: "With or after food" },
                                'after food': { system: "http://snomed.info/sct", code: "311504000", display: "With or after food" },
                                'with or after food': { system: "http://snomed.info/sct", code: "311504000", display: "With or after food" },
                                'before food': { system: "http://snomed.info/sct", code: "307165006", display: "Before food" },
                                'on empty stomach': { system: "http://snomed.info/sct", code: "307165006", display: "Before food" },
                                'with water': { system: "http://snomed.info/sct", code: "418577003", display: "Take with water" },
                                'twice in a day': { system: "http://snomed.info/sct", code: "311504000", display: "With or after food" },
                                'once daily': { system: "http://snomed.info/sct", code: "311504000", display: "With or after food" },
                                'as needed': { system: "http://snomed.info/sct", code: "182856006", display: "Drug treatment stopped - medical advice" }
                            };

                            const normalizedInstruction = instruction.toLowerCase().trim();
                            return instructionMap[normalizedInstruction] || instructionMap['with or after food'];
                        };

                        const instructionCode = getAdditionalInstructionCode(value);
                        dosageInstructions.additionalInstruction = [
                            getSnomedCtCode(instructionCode.code, instructionCode.display)
                        ];
                    } catch (error) {
                        console.log("error in additional instruction", error);
                        throw error;
                    }
                }
            } else if (key === "route" && typeof value === "string") {
                // Add SNOMED coded route - default to oral if not specified
                const routeValue = value || "Oral Route";
                const routeData = await generateSnomedCtCode(routeValue);
                dosageInstructions.route = getSnomedCtCode(routeData.conceptId, routeData.term);

            } else if (key === "method" && typeof value === "string") {
                // Add SNOMED coded method - default to swallow if not specified
                const methodValue = value || "Swallow";
                const methodData = await generateSnomedCtCode(methodValue);
                dosageInstructions.method = getSnomedCtCode(methodData.conceptId, methodData.term);
            }
        }

        // Add default values if missing to prevent "NA" in PHR app
        if (!dosageInstructions.text) {
            dosageInstructions.text = "Take as directed";
        }

        if (!dosageInstructions.route) {
            const defaultRouteData = await generateSnomedCtCode("Oral Route");
            dosageInstructions.route = getSnomedCtCode(defaultRouteData.conceptId, defaultRouteData.term);
        }

        if (!dosageInstructions.method) {
            const defaultMethodData = await generateSnomedCtCode("Swallow");
            dosageInstructions.method = getSnomedCtCode(defaultMethodData.conceptId, defaultMethodData.term);
        }

        if (!dosageInstructions.timing) {
            // Add default timing if missing
            dosageInstructions.timing = {
                repeat: {
                    frequency: 1,
                    period: 1,
                    periodUnit: "d"
                }
            };
        }

        finalDosageInstructions.push(dosageInstructions);
    }

    // Ensure at least one dosage instruction exists
    if (finalDosageInstructions.length === 0) {
        const defaultRouteData = await generateSnomedCtCode("Oral Route");
        const defaultMethodData = await generateSnomedCtCode("Swallow");

        finalDosageInstructions.push({
            text: "Take as directed",
            timing: {
                repeat: {
                    frequency: 1,
                    period: 1,
                    periodUnit: "d"
                }
            },
            route: getSnomedCtCode(defaultRouteData.conceptId, defaultRouteData.term),
            method: getSnomedCtCode(defaultMethodData.conceptId, defaultMethodData.term)
        });
    }

    return {
        fullUrl: `urn:uuid:${requestId}`,
        resource: {
            resourceType: 'MedicationRequest',
            id: requestId,
            meta: medicationRequestMetadata(),
            text: {
                status: "generated",
                div: `<div xmlns="http://www.w3.org/1999/xhtml"><p><b>Medication Request</b></p><p><b>Status:</b> ${status}</p><p><b>Intent:</b> ${intent}</p><p><b>Medication:</b> ${getSnomedDataMedication.term}</p><p><b>Patient:</b> ${patientResource.resource.name?.[0]?.text || 'Patient'}</p></div>`
            },
            status,
            intent: intent,
            authoredOn: authoredOn,
            medicationCodeableConcept: getSnomedCtCode(getSnomedDataMedication.conceptId, toTitleCase(getSnomedDataMedication.term)),
            subject: {
                reference: `urn:uuid:${patientResource.resource.id}`,
                display: patientResource.resource.resourceType
            },
            requester: matchingPractitioner ? {
                reference: `urn:uuid:${matchingPractitioner.resource.id}`,
                display: matchingPractitioner.resource.resourceType
            } : null,
            ...(matchedConditions.length > 0 ? {
                reasonReference: matchedConditions.map(condition => ({
                    reference: `urn:uuid:${condition.resource.id}`,
                    display: toTitleCase(condition.resource.resourceType)
                }))
            } : {}),
            ...(getSnomedDataMedicationReasons.length > 0 ? {
                reasonCode: getSnomedDataMedicationReasons
            } : {}),
            dosageInstruction: finalDosageInstructions
        }
    };
};
