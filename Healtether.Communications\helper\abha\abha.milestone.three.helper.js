import path from "path";
import fs from "fs";
import {
  AbhaResponse,
  CONSENT_REQUEST_INIT,
  CONSENT_REQUEST_ON_NOTIFY,
  CONSENT_REQUEST_STATUS,
  DATA_FLOW_HEALTHINFORMATION_REQUEST,
  FETCH_CONSENT,
  ON_NOTIFY_CONSENT_REQUEST,
  DATA_PUSH_URL,
  SESSION_API_URL,
} from "../../utils/abha.api.js";
import crypto from "crypto";
import { v4 as uuidv4 } from "uuid";
import { AbdmAccessToken } from "../abha/abdm-access-token.js";
import { writeFileSync } from "fs";
const base_url = process.env.ABHA_M3_BASE_URL;
import { generateKeyMaterialCore } from "../../controllers/fidelius.controller.js";
import portalDb from "../../config/clinics.collections.config.js";
// import portalDb from "../../config/clinics.collections.config.js";
const AbhaConsentsModel = portalDb.model("abhaConsents");
// import {AbhaConsentsModel} from "../../schema/consent.schema.js"

export const initiateConsentRequestHelper = async (data) => {
  const consentRequestData = {
    consent: {
      hip: null,
      hiu: {
        id: data.hiu || "IN2410000949",
      },
      hiTypes: data.hiTypes,
      patient: {
        id: data.abhaAddress,
      },
      purpose: {
        code: data.purposeCode || "CAREMGT",
        text: data.purposeText || "Care Management",
        refUri: "www.abdm.gov.in",
      },
      careContexts: null,
      requester: {
        name: data.requesterName,
        identifier: {
          type: "REGNO",
          value: "MH1002",
          system: "https://www.mciindia.org",
        },
      },
      permission: {
        dateRange: {
          to: data.dateRange.endDate,
          from: data.dateRange.startDate,
        },
        frequency: {
          unit: "DAY",
          value: 0,
          repeats: 0,
        },
        accessMode: "VIEW",
        dataEraseAt: data.expiryDate,
      },
    },
  };

  const result = await fetchAbhaApi(
    CONSENT_REQUEST_INIT,
    consentRequestData,
    "POST",
    { "X-CM-ID": "sbx" }
  );
  if (result.ok) {
    await AbhaConsentsModel.create({
      ...data,
      ...consentRequestData.consent,
      requestId: result.requestId,
      consentStatusUpdatedOn: new Date().toISOString(),
      hiuId: data.hiu || "IN2410000949",
      abhaAddress: data.abhaAddress || "testm_233@sbx",
    });
  }
  console.log("===>", result);
  const responseBody =
    result?.headers?.get("content-length") > 0 ? await result.json() : null;

  return new AbhaResponse(result.ok, responseBody);
};

export const checkConsentStatusHelper = async (reqBody, HUIId, req) => {
  const result = await fetchAbhaApi(CONSENT_REQUEST_STATUS, reqBody, "POST", {
    "X-CM-ID": "sbx",
    "X-HIU-ID": HUIId,
  });
  if (result.ok) {
    let updateStatus = await AbhaConsentsModel.updateOne(
      { consentRequestId: req.body.consentRequest.id },
      {
        $set: {
          requestId: result.requestId,
        },
      }
    );
    console.log("updated requestId", result.requestId);
  }
  const responseBody =
    result.headers.get("content-length") > 0 ? await result.json() : null;
  console.log(result.ok);
  return new AbhaResponse(result.ok, responseBody);
};

export const notifyConsentRequestHelper = async (notifyData, hiuId) => {
  const result = await fetchAbhaApi(
    CONSENT_REQUEST_ON_NOTIFY,
    notifyData,
    "POST",
    {
      "X-CM-ID": "sbx",
      "X-HIU-ID": hiuId,
    }
  );

  const responseBody =
    result.headers.get("content-length") > 0 ? await result.json() : null;
  return new AbhaResponse(result.ok, responseBody);
};

export const fetchConsentDetailsHelper = async (consentData, hiuId) => {
  console.log("consentData", consentData, hiuId);
  const result = await fetchAbhaApi(
    FETCH_CONSENT,
    { consentId: consentData },
    "POST",
    {
      "X-CM-ID": "sbx",
      "X-HIU-ID": hiuId || "",
    }
  );
  if (result.ok) {
    await AbhaConsentsModel.findOneAndUpdate(
      { "consentArtefacts.id": consentData },
      { $set: { requestId: result.requestId } },
      { new: true }
    );
  }

  console.log("done fetch", result.ok);
  const responseBody =
    result.headers.get("content-length") > 0 ? await result.json() : null;
  return new AbhaResponse(result.ok, responseBody);
};

export const sendHealthInformationRequest = async (data) => {
  const { publicKey, nonce, x509PublicKey, privateKey } =
    await generateKeyMaterialCore();

  let hiuId = data.consent.consentDetail.hip.id;

  let body = {
    hiRequest: {
      consent: {
        id: data.consent.consentDetail.consentId,
      },
      dateRange: {
        ...data.consent.consentDetail.permission.dateRange,
      },
      dataPushUrl: `https://degrees-fifteen-surrounding-bedford.trycloudflare.com${DATA_PUSH_URL}`,
      keyMaterial: {
        cryptoAlg: "ECDH",
        curve: "Curve25519",
        dhPublicKey: {
          expiry: data.consent.consentDetail.permission.dataEraseAt,
          parameters: crypto.randomBytes(32).toString("base64"),
          keyValue: x509PublicKey,
        },
        nonce: nonce,
      },
    },
  };

  console.log("request api body123456789", body);

  const result = await fetchAbhaApi(
    DATA_FLOW_HEALTHINFORMATION_REQUEST,
    body,
    "POST",
    {
      "X-CM-ID": "sbx",
      "X-HIU-ID": hiuId,
    }
  );

  let savedInfo = await AbhaConsentsModel.findOneAndUpdate(
    { "consentArtefacts.id": data.consent.consentDetail.consentId },
    {
      $set: {
        "consentArtefacts.$.hiTypes": data.consent.consentDetail.hiTypes,
        "consentArtefacts.$.purpose": data.consent.consentDetail.purpose,
        "consentArtefacts.$.permission": data.consent.consentDetail.permission,
        "consentArtefacts.$.requestId": result.requestId,
        "consentArtefacts.$.privateKey": privateKey,
        "consentArtefacts.$.nonce": nonce,
      },
    },
    { new: true }
  );
  // console.log("savedInfo1234",savedInfo);
  result.headers.get("content-length") > 0 ? await result.json() : null;
  return new AbhaResponse(result.ok, result);
};

export const sendNotificationRequest = async (transactionId) => {
  let consentData = await AbhaConsentsModel.findOne({
    transactionId: transactionId,
  });
  const requestData = {
    ...req.body,
    notification: {
      consentId,
      transactionId: consentData.transactionId,
      doneAt: consentData.doneAt,
      notifier: {
        type: "HIU",
        id: consentData.hiuId,
      },
      statusNotification: {
        sessionStatus: "TRANSFERRED",
        hipId: consentData.hiuId,
        statusResponses: [
          {
            careContextReference:
              consentData.careContexts[0].careContextReference,
            hiStatus: "OK",
            description: "Care Management",
          },
        ],
      },
    },
  };
  const result = await fetchAbhaApi(
    ON_NOTIFY_CONSENT_REQUEST,
    requestData,
    "POST",
    {
      "X-CM-ID": "sbx",
      "X-HIU-ID": hiuId,
    }
  );

  const responseBody =
    result.headers.get("content-length") > 0 ? await result.json() : null;

  return new AbhaResponse(result.ok, responseBody);
};

export const fetchAbhaApi = async (url, body, method, header = {}, options = {}) => {
  const {
    maxRetries = 3,
    baseDelay = 1000,
    timeout = 30000,
    retryCount = 0
  } = options;

  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);

  try {
    const token = await AbdmAccessToken();
    const isoTimestamp = new Date().toISOString();
    const randomUUID = uuidv4();

    console.log(`fetchAbhaApi M3 attempt ${retryCount + 1}/${maxRetries + 1} - randomUUID:`, randomUUID);
    const defaultHeaders = {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
      TIMESTAMP: isoTimestamp,
      "REQUEST-ID": randomUUID,
      Accept: "*/*",
      Connection: "keep-alive",
    };

    const finalHeaders = { ...defaultHeaders, ...header };

    const requestOptions = {
      method: method || "POST",
      headers: finalHeaders,
      signal: controller.signal,
    };

    if (body && method !== "GET" && method !== "HEAD") {
      requestOptions.body = JSON.stringify(body);
    }

    const result = await fetch(`${base_url}${url}`, requestOptions);

    clearTimeout(timeoutId);

    console.log(`${base_url}${url}`, result.ok, result.status);

    // Handle HTTP errors that should be retried (5xx errors)
    if (!result.ok && result.status >= 500 && retryCount < maxRetries) {
      const delay = baseDelay * Math.pow(2, retryCount);
      console.log(`HTTP ${result.status} error detected. Retrying in ${delay}ms... (attempt ${retryCount + 1}/${maxRetries})`);

      await new Promise(resolve => setTimeout(resolve, delay));
      return fetchAbhaApi(url, body, method, header, {
        ...options,
        retryCount: retryCount + 1
      });
    }

    // Clone the response to avoid consuming the body multiple times
    const responseClone = result.clone();
    const responseText = await responseClone.text();

    return {
      ok: result.ok,
      status: result.status,
      statusText: result.statusText,
      headers: result.headers,
      url: result.url,
      requestId: randomUUID, // Correctly adding requestId
      json: async () => {
        try {
          return JSON.parse(responseText);
        } catch (e) {
          console.error("Error parsing JSON:", e);
          throw new Error("Invalid JSON response");
        }
      },
      text: async () => responseText,
    };
  } catch (error) {
    clearTimeout(timeoutId);
    console.error(`Error in fetchAbhaApi M3 (attempt ${retryCount + 1}):`, error);

    // Create detailed error information
    const errorDetails = {
      message: error.message,
      code: error.code || "UNKNOWN",
      name: error.name,
      url: `${base_url}${url}`,
      attempt: retryCount + 1,
      maxRetries: maxRetries + 1
    };

    // Determine if we should retry based on error type
    const shouldRetry = retryCount < maxRetries && (
      error.code === "ECONNRESET" ||
      error.code === "ECONNREFUSED" ||
      error.code === "ETIMEDOUT" ||
      error.code === "ENOTFOUND" ||
      error.name === "AbortError" ||
      error.message.includes("timeout") ||
      error.message.includes("refused") ||
      error.message.includes("reset")
    );

    if (shouldRetry) {
      const delay = baseDelay * Math.pow(2, retryCount);
      console.log(`${error.code || error.name} detected. Retrying in ${delay}ms... (attempt ${retryCount + 1}/${maxRetries})`);

      await new Promise(resolve => setTimeout(resolve, delay));
      return fetchAbhaApi(url, body, method, header, {
        ...options,
        retryCount: retryCount + 1
      });
    }

    // If we've exhausted retries or it's a non-retryable error, throw with details
    const finalError = new Error(`fetchAbhaApi M3 failed after ${retryCount + 1} attempts: ${error.message}`);
    finalError.details = errorDetails;
    finalError.originalError = error;
    throw finalError;
  }
};
