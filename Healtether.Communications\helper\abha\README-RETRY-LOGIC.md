# ABHA API Retry Logic Implementation

## Overview

All `fetchAbhaApi` functions in the ABHA helper modules now include robust retry logic to handle timeout errors, connection issues, and server errors. This implementation provides automatic retries with exponential backoff for improved reliability.

## Features

- **Automatic Timeout Handling**: 30-second default timeout with <PERSON>bort<PERSON><PERSON>roller
- **Exponential Backoff**: Retry delays increase exponentially (1s, 2s, 4s, 8s...)
- **Smart Error Detection**: Retries on network errors, timeouts, and 5xx HTTP errors
- **Configurable Options**: Customizable retry count, delays, and timeouts
- **Detailed Error Logging**: Comprehensive error information for debugging

## Updated Functions

### 1. `fetchAbhaApi` in `milestone.two.helper.js`
```javascript
fetchAbhaApi(url, body, method, header = {}, pushurl, options = {})
```

### 2. `fetchAbhaApi` in `abha.milestone.three.helper.js`
```javascript
fetchAbhaApi(url, body, method, header = {}, options = {})
```

### 3. `fetchAbhaApi` in `abdm.helper.js`
```javascript
fetchAbhaApi(url, body, method, txnId, tToken, xToken, options = {})
```

### 4. `fetchAbdmSession` in `abdm.helper.js`
```javascript
fetchAbdmSession(data, options = {})
```

## Default Configuration

```javascript
const DEFAULT_RETRY_CONFIG = {
    maxRetries: 3,        // Maximum number of retry attempts
    baseDelay: 1000,      // Base delay in milliseconds (1 second)
    timeout: 30000        // Request timeout in milliseconds (30 seconds)
};
```

## Usage Examples

### Basic Usage (uses default retry settings)
```javascript
// All existing calls will work with default retry logic
const result = await fetchAbhaApi(url, body, "POST", headers);
```

### Custom Retry Configuration
```javascript
// Custom retry options
const retryOptions = {
    maxRetries: 5,        // Retry up to 5 times
    baseDelay: 2000,      // Start with 2-second delay
    timeout: 45000        // 45-second timeout
};

const result = await fetchAbhaApi(url, body, "POST", headers, null, retryOptions);
```

### Using Utility Function
```javascript
import { createRetryOptions } from './abdm.helper.js';

// Create custom retry options
const options = createRetryOptions({
    maxRetries: 2,
    timeout: 60000
});

const result = await fetchAbhaApi(url, body, "POST", headers, null, options);
```

## Error Types That Trigger Retries

- **Network Errors**: ECONNRESET, ECONNREFUSED, ETIMEDOUT, ENOTFOUND
- **Timeout Errors**: AbortError (when request times out)
- **Server Errors**: HTTP 5xx status codes (500, 502, 503, 504, etc.)
- **Connection Issues**: Any error message containing "timeout", "refused", or "reset"

## Error Types That Do NOT Trigger Retries

- **Client Errors**: HTTP 4xx status codes (400, 401, 403, 404, etc.)
- **Authentication Errors**: Invalid credentials or tokens
- **Validation Errors**: Malformed requests or invalid data

## Exponential Backoff Formula

```
delay = baseDelay * (2 ^ retryAttempt)

Example with baseDelay = 1000ms:
- Attempt 1: 1000ms (1 second)
- Attempt 2: 2000ms (2 seconds)  
- Attempt 3: 4000ms (4 seconds)
- Attempt 4: 8000ms (8 seconds)
```

## Error Response Structure

When all retries are exhausted, the error includes detailed information:

```javascript
{
  message: "fetchAbhaApi failed after 4 attempts: Connection timeout",
  details: {
    message: "Connection timeout",
    code: "ETIMEDOUT",
    name: "TimeoutError",
    url: "https://api.example.com/endpoint",
    attempt: 4,
    maxRetries: 4
  },
  originalError: [Original Error Object]
}
```

## Logging

The retry logic provides detailed console logging:

```
fetchAbhaApi attempt 1/4 - randomUUID: abc123...
HTTP 503 error detected. Retrying in 1000ms... (attempt 1/3)
fetchAbhaApi attempt 2/4 - randomUUID: def456...
ECONNRESET detected. Retrying in 2000ms... (attempt 2/3)
fetchAbhaApi attempt 3/4 - randomUUID: ghi789...
```

## Best Practices

1. **Use Default Settings**: The default configuration works well for most scenarios
2. **Adjust for Critical Operations**: Increase retries for critical operations
3. **Monitor Logs**: Watch retry patterns to identify infrastructure issues
4. **Handle Final Errors**: Always implement proper error handling for exhausted retries
5. **Consider Rate Limits**: Be mindful of API rate limits when setting retry counts

## Backward Compatibility

All existing function calls will continue to work without modification. The retry logic is enabled by default with sensible defaults.

## Performance Impact

- **Minimal Overhead**: Retry logic adds minimal performance overhead for successful requests
- **Improved Reliability**: Significantly reduces failures due to temporary network issues
- **Configurable Timeouts**: Prevents hanging requests with configurable timeouts

## Example: Critical Operation with Extended Retries

```javascript
// For critical operations like consent requests or data transfers
const criticalRetryOptions = {
    maxRetries: 5,        // More retries for critical operations
    baseDelay: 1500,      // Slightly longer initial delay
    timeout: 60000        // Extended timeout for complex operations
};

try {
    const result = await fetchAbhaApi(
        CONSENT_REQUEST_INIT,
        consentData,
        "POST",
        { "X-CM-ID": "sbx" },
        null,
        criticalRetryOptions
    );

    if (result.ok) {
        console.log("Consent request successful");
    }
} catch (error) {
    console.error("Consent request failed after all retries:", error.details);
    // Handle final failure appropriately
}
```

## Migration Guide

No code changes are required for existing implementations. However, you can optionally add retry configuration for specific use cases:

```javascript
// Before (still works)
const result = await fetchAbhaApi(url, body, "POST", headers);

// After (with custom retry options)
const result = await fetchAbhaApi(url, body, "POST", headers, null, {
    maxRetries: 2,
    timeout: 45000
});
```
