import { Schema } from 'mongoose';

const telecomSchema = new Schema({
    system: String,
    value: String,
    use: String
});

const licenseSchema = new Schema({
    code: String,
    display: String,
    licNo: String
});



const signatureSchema = new Schema({
    who: {
        type: { type: String },
        name: String
    },
    sigFormat: String,
    data: String
});

const addressSchema = new Schema({
    use: String,
    type: String,
    text: String,
    // line: [String],
    city: String,
    state: String,
    district: String,
    postalCode: String,
    country: String
});

const allergyIntoleranceSchema = new Schema({
    type: String,
    clinicalStatus: String,
    verificationStatus: String,
    doctor: String,
    notes: [String]
});

const patientSchema = new Schema({
    id:String,
    abhaNumber: String,
    abhaAddress: String,
    name: {
        text:String,
        prefix:[String]
    },
    gender: String,
    dob: String,
    doctors: [String],
    allergyIntolerances: [allergyIntoleranceSchema],
    telecom: [telecomSchema],
    address:[addressSchema]
});

const generalSchema = new Schema({
    artifact: String,
    hipUrl: String,
    hipIds: [String],
    status: String,
    clientId: String
});

const practitionerSchema = new Schema({
    names: [String],
    licenses: [licenseSchema],
    patient: String,
    gender: String,
    birthDate: String,
    address: [addressSchema],
    telecom: [telecomSchema],
});

const encounterSchema = new Schema({
    status: String,
    startTime: String,
    endTime: String
});


const organizationSchema = new Schema({
    name: String,
    telecom: [telecomSchema],
    licenses: [licenseSchema]
});

const singleObservationSchema = new Schema({
    status: { type: String, required: true },
    code: {
        code: { type: String, required: true },
        display: { type: String, required: true }
    },
    valueQuantity: {
        value: { type: Number, required: true },
        unit: { type: String, required: true },
        code: { type: String, required: true }
    },
    effectiveDateTime: { type: String, required: true }
});

const bloodPressureSchema=new Schema({
    bloodpressure:{

        type:[singleObservationSchema],
        required:true
    }
})

const observationSchema = new Schema({
type:Schema.Types.Mixed,
});

const WellnessRecordSchema = new Schema({
    fhirId: {
        type: String,
        required: true,
        index: true
    },
    general: generalSchema,
    patient: patientSchema,
    practitioners: [practitionerSchema],
    // encounter: encounterSchema,
    organization: organizationSchema,
    observations:{
        type:[Schema.Types.Mixed],
        required:true
    },
    signature: signatureSchema,
    abhaCareContextLinked:{
        type: Boolean,
        default: false
    }
});



export { WellnessRecordSchema };
