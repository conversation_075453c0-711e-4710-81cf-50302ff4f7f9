import { v4 as uuidv4 } from "uuid";
import { generateSnomedCtCode } from "./snomed_ct_code.generator.fhir.js";

 
export const generateFamilyMemberHistoryResource = async (
  familyHistory,
  patientResource
) => {
  const id = uuidv4();


  const getSnomedData = await generateSnomedCtCode(familyHistory.name);

  const getRelationshipCode = (relationshipType) => {
    // Map common relationship types to proper FHIR codes
    const relationshipMap = {
      'father': { system: "http://terminology.hl7.org/CodeSystem/v3-RoleCode", code: "FTH", display: "father" },
      'mother': { system: "http://terminology.hl7.org/CodeSystem/v3-RoleCode", code: "MTH", display: "mother" },
      'brother': { system: "http://terminology.hl7.org/CodeSystem/v3-RoleCode", code: "BRO", display: "brother" },
      'sister': { system: "http://terminology.hl7.org/CodeSystem/v3-RoleCode", code: "SIS", display: "sister" },
      'son': { system: "http://terminology.hl7.org/CodeSystem/v3-RoleCode", code: "SON", display: "son" },
      'daughter': { system: "http://terminology.hl7.org/CodeSystem/v3-RoleCode", code: "DAU", display: "daughter" },
      'grandfather': { system: "http://terminology.hl7.org/CodeSystem/v3-RoleCode", code: "GGRFTH", display: "great grandfather" },
      'grandmother': { system: "http://terminology.hl7.org/CodeSystem/v3-RoleCode", code: "GGRMTH", display: "great grandmother" },
      'uncle': { system: "http://terminology.hl7.org/CodeSystem/v3-RoleCode", code: "UNCLE", display: "uncle" },
      'aunt': { system: "http://terminology.hl7.org/CodeSystem/v3-RoleCode", code: "AUNT", display: "aunt" }
    };

    // Try to extract relationship from notes or use default
    const relationship = familyHistory.relationship?.toLowerCase() || 'father';
    return relationshipMap[relationship] || relationshipMap['father'];
  };

  const getSexCode = (relationship) => {
    // Infer sex from relationship
    const maleRelationships = ['father', 'brother', 'son', 'grandfather', 'uncle'];
    const femaleRelationships = ['mother', 'sister', 'daughter', 'grandmother', 'aunt'];

    const rel = relationship.toLowerCase();
    if (maleRelationships.includes(rel)) {
      return { system: "http://hl7.org/fhir/administrative-gender", code: "male", display: "Male" };
    } else if (femaleRelationships.includes(rel)) {
      return { system: "http://hl7.org/fhir/administrative-gender", code: "female", display: "Female" };
    }
    return { system: "http://hl7.org/fhir/administrative-gender", code: "unknown", display: "Unknown" };
  };

  const relationshipCoding = getRelationshipCode(familyHistory.relationship || 'father');
  const sexCoding = getSexCode(familyHistory.relationship || 'father');

  return {
    fullUrl: `urn:uuid:${id}`,
    resource: {
      resourceType: "FamilyMemberHistory",
      id,
      meta: {
        versionId: "1",
        lastUpdated: new Date().toISOString(),
        profile: [
          "https://nrces.in/ndhm/fhir/r4/StructureDefinition/FamilyMemberHistory"
        ]
      },
      status: "completed",
      patient: {
        reference: `urn:uuid:${patientResource.resource.id}`,
        type: "Patient",
        display: patientResource.resource.name?.[0]?.text || "Patient"
      },
      date: new Date().toISOString().split('T')[0], // Current date in YYYY-MM-DD format
      relationship: {
        coding: [relationshipCoding],
        text: relationshipCoding.display
      },
      sex: {
        coding: [sexCoding],
        text: sexCoding.display
      },
      condition: [
        {
          code: {
            coding: [
              {
                system: "http://snomed.info/sct",
                code: getSnomedData.conceptId,
                display: getSnomedData.term
              }
            ],
            text: getSnomedData.term
          },
          contributedToDeath: familyHistory.contributedToDeath !== undefined ? familyHistory.contributedToDeath : false,
          ...(familyHistory.notes && {
            note: [
              {
                text: familyHistory.notes
              }
            ]
          }),
          ...(familyHistory.duration && familyHistory.duration.value && {
            onsetAge: {
              value: familyHistory.duration.value,
              unit: familyHistory.duration.unit || "years",
              system: "http://unitsofmeasure.org",
              code: familyHistory.duration.unit === "months" ? "mo" :
                    familyHistory.duration.unit === "days" ? "d" : "a"
            }
          })
        }
      ]
    }
  };
};

/**
 * Enhanced relationship mapping function for future use
 * This can be expanded when relationship data is available
 */
export const getRelationshipMapping = (relationshipType) => {
  const relationshipMap = {
    "father": { code: "66839005", display: "Father" },
    "mother": { code: "72705000", display: "Mother" },
    "brother": { code: "70924004", display: "Brother" },
    "sister": { code: "27733009", display: "Sister" },
    "grandfather": { code: "77560008", display: "Grandfather" },
    "grandmother": { code: "25656006", display: "Grandmother" },
    "uncle": { code: "38048003", display: "Uncle" },
    "aunt": { code: "87178005", display: "Aunt" },
    "cousin": { code: "421850000", display: "Cousin" },
    "family_member": { code: "444018008", display: "Family member" }
  };

  return relationshipMap[relationshipType?.toLowerCase()] || relationshipMap["family_member"];
};