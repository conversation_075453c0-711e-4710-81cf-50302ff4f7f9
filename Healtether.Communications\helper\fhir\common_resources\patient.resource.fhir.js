import { v4 as uuidv4 } from "uuid";
import {  patientDiv, patientMetadata, patientAbhaNumberIdentifier, patientAbhaAddressIdentifier } from "../../../utils/fhir.constants.js";
import { toTitleCase } from "../../../utils/titlecase.generator.js";

export const generatePatientResource = async (currentTime, patient) => {
    const id = uuidv4();
    const getAddressWithoutId = (patient) => {
        if (patient.address && patient.address.length > 0) {
          const { _id, ...addressWithoutId } = patient.address[0];
          return addressWithoutId;
        }
        return null;
      };
    return {
        fullUrl: `urn:uuid:${id}`,
        resource: {
            resourceType: 'Patient',
            id,
            meta: patientMetadata(currentTime),
            identifier: [patientAbhaNumberIdentifier(patient.abhaNumber),patientAbhaAddressIdentifier(patient.abhaAddress)],
            name: [{
                text: toTitleCase(patient.name.text),
                ...(patient.name.prefix && patient.name.prefix !== "undefined" && patient.name.prefix !== undefined && patient.name.prefix.length > 0 && !patient.name.prefix.includes("undefined") ? { prefix: patient.name.prefix } : {}),
            }],
            telecom: patient.telecom.map(telecom => ({
                system: telecom.system,
                value: telecom.value,
                use: telecom.use
            })),
            gender: patient.gender,
            birthDate: patient.dob,
            address: getAddressWithoutId(patient) ? [getAddressWithoutId(patient)] : []
            // text: patientDiv()
        }
    }
}