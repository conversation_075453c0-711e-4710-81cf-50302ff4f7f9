import mongoose from "mongoose";
import { CLIENT_COLLECTION, PATIENT_COLLECTION } from "../../mongodb.collection.name.js";

const familyHistorySchema = new mongoose.Schema(
    {
        name: {
            type: String,
            required: true,
        },
        relationship: {
            type: String,
            enum: ['father', 'mother', 'brother', 'sister', 'son', 'daughter', 'grandfather', 'grandmother', 'uncle', 'aunt'],
            default: 'father'
        },
        duration: {
            value: Number,
            unit: String,
        },
        notes: {
            type: String,
        },
        contributedToDeath: {
            type: Boolean,
            default: false
        },
        patient: {
            type: mongoose.Schema.Types.ObjectId,
            ref: PATIENT_COLLECTION
        },
        clinic: {
            type: mongoose.Schema.Types.ObjectId,
            ref: CLIENT_COLLECTION
        },
        created: {
            on: {
                type: Date,
                default: Date.now,
            },
            by: {
                id: String,
                name: {
                    type: String,
                    maxLength: 255,
                },
            },
        },
    },
    {
        versionKey: "1.0",
        timestamps: true,
    }
);

familyHistorySchema.index({name:1,clinic: 1, patient: 1});

//const FamilyHistories = mongoose.model("history_familydiseases", familyHistorySchema);

export {familyHistorySchema};
