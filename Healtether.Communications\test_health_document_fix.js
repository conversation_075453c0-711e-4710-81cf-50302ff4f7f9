import { handleApisComingFromAbhaAppForFhir } from './helper/fhir/main.bundle.fhir.helper.js';

// Test data for Health Document bundle generation
const testHealthDocumentData = {
  body: {
    general: {
      artifact: "HealthDocumentRecord",
      status: "final",
      hipUrl: "https://www.healtether.com",
      clientId: "SBX_003515"
    },
    signature: {
      who: {
        type: "Practitioner"
      },
      sigFormat: "image/jpeg",
      data: "c2lnbmF0dXJlIGRhdGEgaGVyZQ=="
    },
    patient: {
      id: "ee017bc6-b33c-4568-adf6-9c7e31fd7e1a",
      name: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
      gender: "female",
      birthDate: "2003-12-05",
      phone: "**********",
      abhaId: "91-1248-5708-0632",
      abhaAddress: "monika_120512@sbx",
      address: {
        text: "nimma ji ji kho , b<PERSON><PERSON> mandi gate, jiwajiganj, Gird, Gird, Gwalior, Madhya Pradesh",
        city: "GWALIOR",
        state: "MADHYA PRADESH",
        district: "MADHYA PRADESH",
        postalCode: "474001",
        country: "india"
      },
      doctors: ["f0417021-67ba-4162-9262-d07c8422a154"]
    },
    practitioners: [
      {
        id: "f0417021-67ba-4162-9262-d07c8422a154",
        name: "Monikushwah",
        prefix: "Dr.",
        gender: "female",
        phone: "**********",
        licenseNumber: "1234567",
        address: {
          text: "jiwaji ganj",
          postalCode: "474001",
          country: "india"
        }
      }
    ],
    organization: {
      id: "3a4fbd9a-83df-4e16-91b9-656fcfa2e618",
      name: "Test",
      phone: "**********",
      facilityId: "1234567"
    },
    documentReferences: [
      {
        status: "current",
        docStatus: "final",
        type: {
          coding: [
            {
              system: "http://snomed.info/sct",
              code: "419891008",
              display: "Record artifact"
            }
          ]
        },
        content: [
          {
            attachment: {
              contentType: "application/pdf",
              title: "Health Document",
              url: "https://example.com/health-document.pdf",
              creation: "2025-06-04T05:44:46.968+05:30"
            }
          }
        ]
      }
    ]
  }
};

// Test the Health Document bundle generation
async function testHealthDocumentGeneration() {
  try {
    console.log('Testing Health Document bundle generation with fix...');
    
    const result = await handleApisComingFromAbhaAppForFhir(testHealthDocumentData);
    
    console.log('✅ Health Document bundle generated successfully!');
    console.log('Bundle ID:', result.id);
    console.log('Entry count:', result.entry.length);
    
    // Check if composition has entries in section
    const composition = result.entry.find(entry => entry.resource.resourceType === 'Composition');
    if (composition && composition.resource.section && composition.resource.section[0].entry) {
      console.log('✅ Section entries found:', composition.resource.section[0].entry.length);
    } else {
      console.log('❌ Section entries still empty');
    }
    
    return result;
  } catch (error) {
    console.error('❌ Error generating Health Document bundle:', error);
    throw error;
  }
}

// Run the test
testHealthDocumentGeneration()
  .then(() => {
    console.log('\n🎉 Test completed successfully!');
    console.log('New bundle saved to: generated_fhir_bundle/fhirHealthDocumentBundle.json');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 Test failed:', error.message);
    process.exit(1);
  });
