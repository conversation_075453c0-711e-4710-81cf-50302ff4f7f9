import {
  abha<PERSON>ardCreation,
  abhaNumberAddressotp,
  enrollMobileNoOTP,
  enrollUsingAadhaar,
  enrollUsingAbhaddress,
  enrollUsingMobile,
  getAbhaAddressCard,
  getAbhaAddressSuggestion,
  getAbhaCard,
  loginUsingAadhar,
  loginUsingIndexMobile,
  patientProfileOnShare,
  qrCode,
  searchLogin,
  userAbhaAddressProfile,
  userProfile,
  verifyAbhanumberAddressOtp,
  verifyMobileNoOtp,
} from "../helper/abha/milestone.one.helper.js";

export const abhaSearchLogin = async (req, res) => {
  const { mobile } = req.body;
  var result = await searchLogin(mobile);
  res.status(result.isSuccess ? 200 : 500).json(result);
};


// one for four 
export const handleAbhaNumberAddressOtpRequest = async (req, res) => {
  const { type, abhaNumberAddress } = req.body;
  const result = await abhaNumberAddressotp({ type, abhaNumberAddress });
  res.status(result.isSuccess ? 200 : 500).json(result);

};



// verifyAbhaNumberAddress otp 
export const handleVerifyAbhaNumberAddressOtp = async (req, res) => {
  const { type, otp, txnId } = req.body;

  const result = await verifyAbhanumberAddressOtp(type, otp, txnId);
  res.status(result.isSuccess ? 200 : 500).json(result);

};

export const indexLogin = async (req, res) => {
  const { index, txnId } = req.body;
  var result = await loginUsingIndexMobile(index, txnId);
  res.status(result.isSuccess ? 200 : 500).json(result);
};

export const verifyMobileOtp = async (req, res) => {
  const { otp, txnId } = req.body;
  var result = await verifyMobileNoOtp(otp, txnId);
  res.status(result.isSuccess ? 200 : 400).json(result);
};

export const getUserProfile = async (req, res) => {
  let xToken = req.headers["x-token"];
  var result = await userProfile(xToken);
  res.status(result.isSuccess ? 200 : 500).json(result);
};
export const getUserAbhaAddressProfile = async (req, res) => {
  let xToken = req.headers["x-token"];
  var result = await userAbhaAddressProfile(xToken);
  res.status(result.isSuccess ? 200 : 500).json(result);
};


export const aadhaarLogin = async (req, res) => {
  const { aadhaarNumber } = req.body;

  var result = await loginUsingAadhar(aadhaarNumber);
  res.status(result.isSuccess ? 200 : 500).json(result);
};


export const getUserAbhaCard = async (req, res) => {
  let xToken = req.headers["x-token"];
  var result = await getAbhaCard(xToken);
  if (result.isSuccess) {
    // Set headers for the image response
    res.setHeader("Content-Type", "image/png");
    res.setHeader("Content-Disposition", 'inline; filename="abha-card.png"');

    // Send the image buffer as the response
    return res.status(200).send(result.response);
  }
};
export const getUserAbhaAddressCard = async (req, res) => {
  let xToken = req.headers["x-token"];
  var result = await getAbhaAddressCard(xToken);
  if (result.isSuccess) {
    // Set headers for the image response
    res.setHeader("Content-Type", "image/png");
    res.setHeader("Content-Disposition", 'inline; filename="abha-card.png"');

    // Send the image buffer as the response
    return res.status(200).send(result.response);
  }
};

export const getQRCode = async (req, res) => {
  let xToken = req.headers["x-token"];
  var result = await qrCode(xToken);

  res.setHeader("Content-Type", "image/png");
  res.setHeader("Content-Disposition", 'inline; filename="abha-card.png"');
  res.status(result.isSuccess ? 200 : 400).json(result.response);
};

export const createAbhaCard = async (req, res) => {
  const { aadhaarNumber } = req.body;
  var result = await abhaCardCreation(aadhaarNumber);
  res.status(result.isSuccess ? 200 : 500).json(result);
};


export const enrollByAadhaar = async (req, res) => {
  const { mobileNumber, otp, txnId } = req.body;
  var result = await enrollUsingAadhaar(mobileNumber, otp, txnId);

  res.status(result.isSuccess ? 200 : 500).json(result);
};

export const enrollbyMobile = async (req, res) => {
  const { mobile, txnId } = req.body;
  var result = await enrollUsingMobile(mobile, txnId);

  res.status(result.isSuccess ? 200 : 500).json(result);
};

export const enrollMobileOTP = async (req, res) => {
  const { mobileNumber, otp, txnId } = req.body;
  var result = await enrollMobileNoOTP(mobileNumber, otp, txnId);

  res.status(result.isSuccess ? 200 : 500).json(result);
};

export const getSuggestion = async (req, res) => {
  const { txnId } = req.body;
  var result = await getAbhaAddressSuggestion(txnId);

  res.status(result.isSuccess ? 200 : 500).json(result);
};

export const enrollByAbhaddress = async (req, res) => {
  const { txnId, abhaAddress, preferred } = req.body;
  var result = await enrollUsingAbhaddress(txnId, abhaAddress, preferred);

  res.status(result.isSuccess ? 200 : 500).json(result);
};

export const profileOnShare = async (req, res) => {
  const { acknowledgement, response } = req.body;
  const result = await patientProfileOnShare(acknowledgement, response, req);

  return res.status(result.ok ? 200 : 500).json(result)

};