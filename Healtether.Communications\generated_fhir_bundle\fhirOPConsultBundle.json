{"resourceType": "Bundle", "id": "7eefccfd-6488-4f3e-8a5c-1e6a52fa3981", "meta": {"versionId": "1", "lastUpdated": "2025-06-04T05:44:46.948+05:30", "profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/DocumentBundle"], "security": [{"system": "http://terminology.hl7.org/CodeSystem/v3-Confidentiality", "code": "V", "display": "very restricted"}]}, "identifier": {"system": "https://www.healtether.com", "value": "SBX_003515"}, "type": "document", "timestamp": "2025-06-04T05:44:46.948+05:30", "entry": [{"fullUrl": "urn:uuid:caf72bab-7608-4df0-8bcd-a69549236a9b", "resource": {"resourceType": "Composition", "id": "caf72bab-7608-4df0-8bcd-a69549236a9b", "meta": {"versionId": "1", "lastUpdated": "2025-06-04T05:44:46.948+05:30", "profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/OPConsultRecord"]}, "identifier": {"system": "https://www.healtether.com", "value": "SBX_003515"}, "language": "en", "status": "final", "type": {"coding": [{"system": "http://snomed.info/sct", "code": "371530004", "display": "Clinical consultation report"}], "text": "Clinical consultation report"}, "date": "2025-06-04T05:44:46.948+05:30", "title": "Consultation Report", "subject": {"reference": "urn:uuid:32e55d0c-1d14-43de-b67c-ad4e6e6d122e", "display": "Patient"}, "encounter": {"reference": "urn:uuid:bb98938e-2ca8-4c04-b233-35d53c599c6c", "display": "Encounter"}, "author": [{"reference": "urn:uuid:503e837d-63bd-4ff2-b9b9-85aef7aca5d0", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "attester": [{"mode": "legal", "party": {"reference": "urn:uuid:503e837d-63bd-4ff2-b9b9-85aef7aca5d0", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"mode": "legal", "party": {"reference": "urn:uuid:eafa2cdb-8e78-405d-aa9a-eee8ddd7377d", "display": "Test"}}], "custodian": {"reference": "urn:uuid:eafa2cdb-8e78-405d-aa9a-eee8ddd7377d", "display": "Test"}, "section": [{"title": "Chief complaints", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "422843007", "display": "Chief complaint section"}], "text": "Chief complaint section"}, "entry": [{"reference": "urn:uuid:ebb6278b-3092-47d3-ba36-53c7ed8b73cb", "display": "Condition"}]}, {"title": "Allergies", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "722446000", "display": "Allergy record"}]}, "entry": [{"reference": "urn:uuid:7f16a07f-0d16-4739-b9ac-3dc4a3863bd3", "display": "AllergyIntolerance"}]}, {"title": "FamilyHistory", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "422432008", "display": "Family history section"}]}, "entry": [{"reference": "urn:uuid:887b75c8-50c0-4340-952e-396cbdf26263", "display": "FamilyMemberHistory"}]}, {"title": "Vital Signs", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "118227000", "display": "Vital signs finding"}]}, "entry": [{"reference": "urn:uuid:ef6bdd15-0cab-4219-acc0-9625414b57f1", "display": "Observation"}, {"reference": "urn:uuid:8ff67872-e463-49b4-a128-8f0a013212a4", "display": "Observation"}, {"reference": "urn:uuid:6071e900-6be4-4551-be1a-2ebc739150fc", "display": "Observation"}, {"reference": "urn:uuid:e9d6a974-62b5-4dff-8e5a-98de7ebf2103", "display": "Observation"}, {"reference": "urn:uuid:05951cee-b48a-47cb-9acc-c5d8c781db27", "display": "Observation"}, {"reference": "urn:uuid:ec9a01f1-3f70-4963-8f08-972a6fb234c1", "display": "Observation"}, {"reference": "urn:uuid:b2127326-5af1-4c31-a925-f965384e420f", "display": "Observation"}, {"reference": "urn:uuid:9225b228-e53b-4d96-85ba-d4cd9bce0aed", "display": "Observation"}]}, {"title": "Procedure", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "371525003", "display": "Clinical procedure report"}]}, "entry": [{"reference": "urn:uuid:957ec948-60b0-485c-855e-9e3e8cc9c559", "display": "Procedure"}]}, {"title": "Follow Up", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "736271009", "display": "Outpatient care plan"}]}, "entry": [{"reference": "urn:uuid:0ab28be4-53a3-42bc-8bae-4393c3ec1ef8", "display": "Appointment"}]}, {"title": "Investigation Advice", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "721963009", "display": "Order document"}]}, "entry": [{"reference": "urn:uuid:7b37dc92-039a-43f8-ac2b-9f789c53b6cb", "display": "ServiceRequest"}]}, {"title": "Medications", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "721912009", "display": "Medication summary document"}]}, "entry": [{"reference": "urn:uuid:7e88490c-4e95-4523-8c60-75194c89ad78", "display": "MedicationStatement"}, {"reference": "urn:uuid:2f7fc1f1-5e5a-4d12-8fc6-edd8836981df", "display": "MedicationRequest"}]}, {"title": "Document Reference", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "371530004", "display": "Clinical consultation report"}]}, "entry": []}]}}, {"fullUrl": "urn:uuid:503e837d-63bd-4ff2-b9b9-85aef7aca5d0", "resource": {"resourceType": "Practitioner", "id": "503e837d-63bd-4ff2-b9b9-85aef7aca5d0", "meta": {"versionId": "1", "lastUpdated": "2025-06-04T05:44:46.948+05:30", "profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Practitioner"]}, "identifier": [{"type": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/v2-0203", "code": "MD", "display": "Medical License number"}]}, "system": "https://doctor.ndhm.gov.in", "value": "1234567"}], "name": [{"use": "official", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "prefix": ["Dr."]}], "telecom": [{"system": "phone", "value": "**********", "use": "mobile"}], "gender": "female", "address": [{"use": "home", "type": "physical", "text": "<PERSON><PERSON><PERSON> ganj", "postalCode": "474001", "country": "india"}]}}, {"fullUrl": "urn:uuid:eafa2cdb-8e78-405d-aa9a-eee8ddd7377d", "resource": {"resourceType": "Organization", "id": "eafa2cdb-8e78-405d-aa9a-eee8ddd7377d", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Organization"]}, "identifier": [{"type": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/v2-0203", "code": "PRN", "display": "Provider number"}]}, "system": "https://facility.ndhm.gov.in", "value": "1234567"}], "name": "Test", "telecom": [{"system": "phone", "value": "**********", "use": "work"}]}}, {"fullUrl": "urn:uuid:32e55d0c-1d14-43de-b67c-ad4e6e6d122e", "resource": {"resourceType": "Patient", "id": "32e55d0c-1d14-43de-b67c-ad4e6e6d122e", "meta": {"versionId": "1", "lastUpdated": "2025-06-04T05:44:46.948+05:30", "profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Patient"]}, "identifier": [{"type": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/v2-0203", "code": "MR", "display": "Medical record number"}], "text": "ABHAID"}, "system": "https://abha.abdm.gov.in/abha/v3", "value": "91-1248-5708-0632"}, {"type": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/v2-0203", "code": "MR", "display": "Medical record number"}], "text": "ABHAADDRESS"}, "system": "https://abha.abdm.gov.in/abha/v3", "value": "monika_120512@sbx"}], "name": [{"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "telecom": [{"system": "phone", "value": "**********", "use": "mobile"}], "gender": "female", "birthDate": "2003-12-05", "address": [{"use": "home", "type": "physical", "text": "nimma ji ji kho , b<PERSON><PERSON> mandi gate, jiwajiganj, Gird, Gird, Gwalior, Madhya Pradesh", "city": "GWALIOR", "state": "MADHYA PRADESH", "district": "MADHYA PRADESH", "postalCode": "474001", "country": "india"}]}}, {"fullUrl": "urn:uuid:bb98938e-2ca8-4c04-b233-35d53c599c6c", "resource": {"resourceType": "Encounter", "id": "bb98938e-2ca8-4c04-b233-35d53c599c6c", "meta": {"lastUpdated": "2025-06-04T05:44:46.948+05:30", "profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Encounter"]}, "identifier": [{"system": "https://ndhm.in", "value": "hip1"}, {"system": "https://ndhm.in", "value": "hip2"}], "status": "finished", "class": {"system": "http://terminology.hl7.org/CodeSystem/v3-ActCode", "code": "AMB", "display": "ambulatory"}, "subject": {"reference": "urn:uuid:32e55d0c-1d14-43de-b67c-ad4e6e6d122e", "display": "Patient"}, "period": {"start": "2025-06-03T20:31:39.744Z", "end": "2025-06-03T20:31:39.744Z"}, "diagnosis": [{"condition": {"reference": "urn:uuid:ebb6278b-3092-47d3-ba36-53c7ed8b73cb", "display": "Condition"}, "use": {"coding": [{"system": "http://snomed.info/sct", "code": "67626002", "display": "Infection by Moniezia"}], "text": "(Notes: test notes)"}}]}}, {"fullUrl": "urn:uuid:7f16a07f-0d16-4739-b9ac-3dc4a3863bd3", "resource": {"resourceType": "AllergyIntolerance", "id": "7f16a07f-0d16-4739-b9ac-3dc4a3863bd3", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/AllergyIntolerance"]}, "clinicalStatus": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/allergyintolerance-clinical", "code": "active", "display": "active"}]}, "verificationStatus": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/allergyintolerance-verification", "code": "confirmed", "display": "confirmed"}]}, "code": {"coding": [{"system": "http://snomed.info/sct", "code": "75413007", "display": "Peanut"}], "text": "Peanut"}, "recordedDate": "2025-06-04T05:44:46.948+05:30", "patient": {"reference": "urn:uuid:32e55d0c-1d14-43de-b67c-ad4e6e6d122e", "display": "Patient"}, "recorder": {"reference": "urn:uuid:503e837d-63bd-4ff2-b9b9-85aef7aca5d0", "display": "Practitioner"}, "note": [{"text": "no notes"}]}}, {"fullUrl": "urn:uuid:887b75c8-50c0-4340-952e-396cbdf26263", "resource": {"resourceType": "FamilyMemberHistory", "id": "887b75c8-50c0-4340-952e-396cbdf26263", "meta": {"versionId": "1", "lastUpdated": "2025-06-04T00:14:51.915Z", "profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/FamilyMemberHistory"]}, "patient": {"reference": "urn:uuid:32e55d0c-1d14-43de-b67c-ad4e6e6d122e", "type": "Patient", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "relationship": {"coding": [{"system": "http://snomed.info/sct", "code": "444018008", "display": "Person with characteristic related to subject of record"}], "text": "Person with characteristic related to subject of record"}, "status": "completed", "condition": [{"code": {"coding": [{"system": "http://snomed.info/sct", "code": "61569007", "display": "Agoraphobia without history of panic disorder (disorder)"}], "text": "Agoraphobia without history of panic disorder (disorder)"}, "note": [{"text": "test note"}], "onsetAge": {"value": 3, "unit": "Days", "system": "http://unitsofmeasure.org", "code": "a"}}]}}, {"fullUrl": "urn:uuid:ef6bdd15-0cab-4219-acc0-9625414b57f1", "resource": {"resourceType": "Observation", "id": "ef6bdd15-0cab-4219-acc0-9625414b57f1", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/ObservationVitalSigns"]}, "status": "final", "category": [{"coding": [{"system": "http://terminology.hl7.org/CodeSystem/observation-category", "code": "vital-signs", "display": "Vital Signs"}], "text": "Vital Signs"}], "code": {"coding": [{"system": "http://loinc.org", "code": "85354-9", "display": "Blood pressure panel with all children optional"}], "text": "Blood pressure panel with all children optional"}, "subject": {"reference": "urn:uuid:32e55d0c-1d14-43de-b67c-ad4e6e6d122e", "type": "Patient", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "effectiveDateTime": "2025-06-04T05:44:46.948+05:30", "issued": "2025-06-04T00:14:51.916Z", "performer": [{"reference": "urn:uuid:503e837d-63bd-4ff2-b9b9-85aef7aca5d0", "type": "Practitioner", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"reference": "urn:uuid:eafa2cdb-8e78-405d-aa9a-eee8ddd7377d", "type": "Organization", "display": "Test"}], "component": [{"code": {"coding": [{"system": "http://loinc.org", "code": "8462-4", "display": "Diastolic blood pressure"}], "text": "Diastolic blood pressure"}, "valueQuantity": {"value": 30, "unit": "mmhg", "system": "http://unitsofmeasure.org", "code": "mm[Hg]"}}, {"code": {"coding": [{"system": "http://loinc.org", "code": "8480-6", "display": "Systolic blood pressure"}], "text": "Systolic blood pressure"}, "valueQuantity": {"value": 120, "unit": "mmhg", "system": "http://unitsofmeasure.org", "code": "mm[Hg]"}}]}}, {"fullUrl": "urn:uuid:8ff67872-e463-49b4-a128-8f0a013212a4", "resource": {"resourceType": "Observation", "id": "8ff67872-e463-49b4-a128-8f0a013212a4", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/ObservationVitalSigns"]}, "status": "final", "category": [{"coding": [{"system": "http://terminology.hl7.org/CodeSystem/observation-category", "code": "vital-signs", "display": "Vital Signs"}], "text": "Vital Signs"}], "code": {"coding": [{"system": "http://loinc.org", "code": "8302-2", "display": "Body height"}], "text": "Body height"}, "subject": {"reference": "urn:uuid:32e55d0c-1d14-43de-b67c-ad4e6e6d122e", "type": "Patient", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "effectiveDateTime": "2025-06-04T05:44:46.948+05:30", "issued": "2025-06-04T00:14:51.916Z", "performer": [{"reference": "urn:uuid:503e837d-63bd-4ff2-b9b9-85aef7aca5d0", "type": "Practitioner", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"reference": "urn:uuid:eafa2cdb-8e78-405d-aa9a-eee8ddd7377d", "type": "Organization", "display": "Test"}], "valueQuantity": {"value": 130, "unit": "cm", "system": "http://unitsofmeasure.org", "code": "cm"}}}, {"fullUrl": "urn:uuid:6071e900-6be4-4551-be1a-2ebc739150fc", "resource": {"resourceType": "Observation", "id": "6071e900-6be4-4551-be1a-2ebc739150fc", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/ObservationVitalSigns"]}, "status": "final", "category": [{"coding": [{"system": "http://terminology.hl7.org/CodeSystem/observation-category", "code": "vital-signs", "display": "Vital Signs"}], "text": "Vital Signs"}], "code": {"coding": [{"system": "http://snomed.info/sct", "code": "27113001", "display": "Body weight"}], "text": "Body weight"}, "subject": {"reference": "urn:uuid:32e55d0c-1d14-43de-b67c-ad4e6e6d122e", "type": "Patient", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "effectiveDateTime": "2025-06-04T05:44:46.948+05:30", "issued": "2025-06-04T00:14:51.916Z", "performer": [{"reference": "urn:uuid:503e837d-63bd-4ff2-b9b9-85aef7aca5d0", "type": "Practitioner", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"reference": "urn:uuid:eafa2cdb-8e78-405d-aa9a-eee8ddd7377d", "type": "Organization", "display": "Test"}], "valueQuantity": {"value": 50, "unit": "kg", "system": "http://unitsofmeasure.org", "code": "kg"}}}, {"fullUrl": "urn:uuid:e9d6a974-62b5-4dff-8e5a-98de7ebf2103", "resource": {"resourceType": "Observation", "id": "e9d6a974-62b5-4dff-8e5a-98de7ebf2103", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/ObservationVitalSigns"]}, "status": "final", "category": [{"coding": [{"system": "http://terminology.hl7.org/CodeSystem/observation-category", "code": "vital-signs", "display": "Vital Signs"}], "text": "Vital Signs"}], "code": {"coding": [{"system": "http://loinc.org", "code": "61008-9", "display": "Body surface temperature"}], "text": "Body surface temperature"}, "subject": {"reference": "urn:uuid:32e55d0c-1d14-43de-b67c-ad4e6e6d122e", "type": "Patient", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "effectiveDateTime": "2025-06-04T05:44:46.948+05:30", "issued": "2025-06-04T00:14:51.916Z", "performer": [{"reference": "urn:uuid:503e837d-63bd-4ff2-b9b9-85aef7aca5d0", "type": "Practitioner", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"reference": "urn:uuid:eafa2cdb-8e78-405d-aa9a-eee8ddd7377d", "type": "Organization", "display": "Test"}], "valueQuantity": {"value": 34, "unit": "degF", "system": "http://unitsofmeasure.org", "code": "[degF]"}}}, {"fullUrl": "urn:uuid:05951cee-b48a-47cb-9acc-c5d8c781db27", "resource": {"resourceType": "Observation", "id": "05951cee-b48a-47cb-9acc-c5d8c781db27", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/ObservationVitalSigns"]}, "status": "final", "category": [{"coding": [{"system": "http://terminology.hl7.org/CodeSystem/observation-category", "code": "vital-signs", "display": "Vital Signs"}], "text": "Vital Signs"}], "code": {"coding": [{"system": "http://snomed.info/sct", "code": "78564009", "display": "Pulse rate"}], "text": "Pulse rate"}, "subject": {"reference": "urn:uuid:32e55d0c-1d14-43de-b67c-ad4e6e6d122e", "type": "Patient", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "effectiveDateTime": "2025-06-04T05:44:46.948+05:30", "issued": "2025-06-04T00:14:51.916Z", "performer": [{"reference": "urn:uuid:503e837d-63bd-4ff2-b9b9-85aef7aca5d0", "type": "Practitioner", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"reference": "urn:uuid:eafa2cdb-8e78-405d-aa9a-eee8ddd7377d", "type": "Organization", "display": "Test"}], "valueQuantity": {"value": 50, "unit": "beats/min", "system": "http://unitsofmeasure.org", "code": "/min"}}}, {"fullUrl": "urn:uuid:ec9a01f1-3f70-4963-8f08-972a6fb234c1", "resource": {"resourceType": "Observation", "id": "ec9a01f1-3f70-4963-8f08-972a6fb234c1", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/ObservationVitalSigns"]}, "status": "final", "category": [{"coding": [{"system": "http://terminology.hl7.org/CodeSystem/observation-category", "code": "vital-signs", "display": "Vital Signs"}], "text": "Vital Signs"}], "code": {"coding": [{"system": "http://snomed.info/sct", "code": "86290005", "display": "Respiratory rate"}], "text": "Respiratory rate"}, "subject": {"reference": "urn:uuid:32e55d0c-1d14-43de-b67c-ad4e6e6d122e", "type": "Patient", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "effectiveDateTime": "2025-06-04T05:44:46.948+05:30", "issued": "2025-06-04T00:14:51.916Z", "performer": [{"reference": "urn:uuid:503e837d-63bd-4ff2-b9b9-85aef7aca5d0", "type": "Practitioner", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"reference": "urn:uuid:eafa2cdb-8e78-405d-aa9a-eee8ddd7377d", "type": "Organization", "display": "Test"}], "valueQuantity": {"value": 40, "unit": "breaths/min", "system": "http://unitsofmeasure.org", "code": "/min"}}}, {"fullUrl": "urn:uuid:b2127326-5af1-4c31-a925-f965384e420f", "resource": {"resourceType": "Observation", "id": "b2127326-5af1-4c31-a925-f965384e420f", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/ObservationVitalSigns"]}, "status": "final", "category": [{"coding": [{"system": "http://terminology.hl7.org/CodeSystem/observation-category", "code": "vital-signs", "display": "Vital Signs"}], "text": "Vital Signs"}], "code": {"coding": [{"system": "http://snomed.info/sct", "code": "250554003", "display": "Measurement of oxygen saturation at periphery"}], "text": "Measurement of oxygen saturation at periphery"}, "subject": {"reference": "urn:uuid:32e55d0c-1d14-43de-b67c-ad4e6e6d122e", "type": "Patient", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "effectiveDateTime": "2025-06-04T05:44:46.948+05:30", "issued": "2025-06-04T00:14:51.916Z", "performer": [{"reference": "urn:uuid:503e837d-63bd-4ff2-b9b9-85aef7aca5d0", "type": "Practitioner", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"reference": "urn:uuid:eafa2cdb-8e78-405d-aa9a-eee8ddd7377d", "type": "Organization", "display": "Test"}], "valueQuantity": {"value": 40, "unit": "%", "system": "http://unitsofmeasure.org", "code": "%"}}}, {"fullUrl": "urn:uuid:9225b228-e53b-4d96-85ba-d4cd9bce0aed", "resource": {"resourceType": "Observation", "id": "9225b228-e53b-4d96-85ba-d4cd9bce0aed", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/ObservationVitalSigns"]}, "status": "final", "category": [{"coding": [{"system": "http://terminology.hl7.org/CodeSystem/observation-category", "code": "vital-signs", "display": "Vital Signs"}], "text": "Vital Signs"}], "code": {"coding": [{"system": "http://snomed.info/sct", "code": "269864005", "display": "Blood glucose result"}], "text": "Blood glucose result"}, "subject": {"reference": "urn:uuid:32e55d0c-1d14-43de-b67c-ad4e6e6d122e", "type": "Patient", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "effectiveDateTime": "2025-06-04T05:44:46.948+05:30", "issued": "2025-06-04T00:14:51.916Z", "performer": [{"reference": "urn:uuid:503e837d-63bd-4ff2-b9b9-85aef7aca5d0", "type": "Practitioner", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"reference": "urn:uuid:eafa2cdb-8e78-405d-aa9a-eee8ddd7377d", "type": "Organization", "display": "Test"}], "valueQuantity": {"value": 50, "unit": "mg/dL", "system": "http://unitsofmeasure.org", "code": "mg/dL"}}}, {"fullUrl": "urn:uuid:0ab28be4-53a3-42bc-8bae-4393c3ec1ef8", "resource": {"resourceType": "Appointment", "id": "0ab28be4-53a3-42bc-8bae-4393c3ec1ef8", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Appointment"]}, "status": "booked", "description": "Follow-up consultation", "start": "2023-10-01T10:00:00+05:30", "end": "2023-10-01T11:00:00+05:30", "created": "2025-06-03T20:31:39.744Z", "serviceCategory": [{"coding": [{"system": "http://snomed.info/sct", "code": "11429006", "display": "Consultation"}], "text": "Consultation"}], "serviceType": [{"coding": [{"system": "http://snomed.info/sct", "code": "60132005", "display": "General"}], "text": "General"}], "appointmentType": {"coding": [{"system": "http://snomed.info/sct", "code": "11429006", "display": "Consultation"}], "text": "Consultation"}, "specialty": [{"coding": [{"system": "http://snomed.info/sct", "code": "394579002", "display": "Cardiology"}], "text": "Cardiology"}], "participant": [{"actor": {"reference": "urn:uuid:32e55d0c-1d14-43de-b67c-ad4e6e6d122e", "display": "Patient"}, "status": "accepted"}, {"actor": {"reference": "urn:uuid:503e837d-63bd-4ff2-b9b9-85aef7aca5d0", "display": "Practitioner"}, "status": "accepted"}], "text": {"status": "generated", "div": "<div xmlns='http://www.w3.org/1999/xhtml' lang='en' xml:lang='en'><p>Appointment Record</p></div>"}}}, {"fullUrl": "urn:uuid:ebb6278b-3092-47d3-ba36-53c7ed8b73cb", "resource": {"resourceType": "Condition", "id": "ebb6278b-3092-47d3-ba36-53c7ed8b73cb", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Condition"]}, "clinicalStatus": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/condition-clinical", "code": "active", "display": "Active"}]}, "code": {"coding": [{"system": "http://snomed.info/sct", "code": "67626002", "display": "Infection By Moniezia"}], "text": "(Notes: test notes)"}, "recordedDate": "2025-06-04T00:13:56.612Z", "onsetPeriod": {"start": "2025-06-04T00:13:56.612Z", "end": "2025-06-04T00:13:56.612Z"}, "subject": {"reference": "urn:uuid:32e55d0c-1d14-43de-b67c-ad4e6e6d122e", "display": "Patient"}}}, {"fullUrl": "urn:uuid:957ec948-60b0-485c-855e-9e3e8cc9c559", "resource": {"resourceType": "Procedure", "id": "957ec948-60b0-485c-855e-9e3e8cc9c559", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Procedure"]}, "status": "completed", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "439719009", "display": "Pasteurization (procedure)"}], "text": "(Duration:3 Days) (Notes: test notes)"}, "subject": {"reference": "urn:uuid:32e55d0c-1d14-43de-b67c-ad4e6e6d122e", "display": "Patient"}, "performedDateTime": "2023-10-01T10:30:00+05:30", "followUp": [{"coding": [{"system": "http://snomed.info/sct", "code": "281036007", "display": "Follow-up consultation"}], "text": "Follow-up consultation"}], "text": {"status": "generated", "div": "<div xmlns='http://www.w3.org/1999/xhtml' lang='en' xml:lang='en'><p>Procedure Record</p></div>"}}}, {"fullUrl": "urn:uuid:7b37dc92-039a-43f8-ac2b-9f789c53b6cb", "resource": {"resourceType": "ServiceRequest", "id": "7b37dc92-039a-43f8-ac2b-9f789c53b6cb", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/ServiceRequest"]}, "status": "completed", "intent": "order", "authoredOn": "2025-06-04T05:44:46.948+05:30", "category": [{"coding": [{"system": "http://snomed.info/sct", "code": "396550006", "display": "blood test"}], "text": "(Notes: test notes)"}], "code": {"coding": [{"system": "http://snomed.info/sct", "code": "15220000", "display": "Laboratory test"}], "text": "Laboratory test"}, "subject": {"reference": "urn:uuid:32e55d0c-1d14-43de-b67c-ad4e6e6d122e", "display": "Patient"}, "requester": {"reference": "urn:uuid:503e837d-63bd-4ff2-b9b9-85aef7aca5d0", "display": "Practitioner"}, "text": {"status": "generated", "div": "<div xmlns='http://www.w3.org/1999/xhtml' lang='en' xml:lang='en'><p>ServiceRequest Record</p></div>"}}}, {"fullUrl": "urn:uuid:7e88490c-4e95-4523-8c60-75194c89ad78", "resource": {"resourceType": "MedicationStatement", "id": "7e88490c-4e95-4523-8c60-75194c89ad78", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/MedicationStatement"]}, "status": "completed", "dateAsserted": "2025-06-04T05:44:46.948+05:30", "medicationCodeableConcept": {"coding": [{"system": "http://snomed.info/sct", "code": "134463001", "display": "Telmisartan 20 mg oral tablet"}], "text": "Telmisartan 20 mg oral tablet"}, "subject": {"reference": "urn:uuid:32e55d0c-1d14-43de-b67c-ad4e6e6d122e", "display": "Patient"}}}, {"fullUrl": "urn:uuid:2f7fc1f1-5e5a-4d12-8fc6-edd8836981df", "resource": {"resourceType": "MedicationRequest", "id": "2f7fc1f1-5e5a-4d12-8fc6-edd8836981df", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/MedicationRequest"]}, "status": "active", "intent": "order", "authoredOn": "2025-06-04T00:13:56.612Z", "medicationCodeableConcept": {"coding": [{"system": "http://snomed.info/sct", "code": "2377371000189103", "display": "Parawel 250 Mg/5 Ml Oral Suspension"}], "text": "Parawel 250 Mg/5 Ml Oral Suspension"}, "subject": {"reference": "urn:uuid:32e55d0c-1d14-43de-b67c-ad4e6e6d122e", "display": "Patient"}, "requester": {"reference": "urn:uuid:503e837d-63bd-4ff2-b9b9-85aef7aca5d0", "display": "Practitioner"}, "reasonReference": [{"reference": "urn:uuid:ebb6278b-3092-47d3-ba36-53c7ed8b73cb", "display": "Condition"}], "dosageInstruction": [{"text": "test notes only"}], "reasonCode": [{"coding": [{"system": "http://snomed.info/sct", "code": "397991007", "display": "Monitoring Problem"}], "text": "(Duration: 1 Days) (Notes: test notes)"}]}}], "signature": {"type": [{"system": "urn:iso-astm:E1762-95:2013", "code": "1.2.840.10065.1.12.1.1", "display": "Author's Signature"}], "when": "2025-06-04T05:44:46.948+05:30", "who": {"reference": "urn:uuid:503e837d-63bd-4ff2-b9b9-85aef7aca5d0", "display": "Practitioner"}, "sigFormat": "image/jpeg", "data": "c2lnbmF0dXJlIGRhdGEgaGVyZQ=="}}