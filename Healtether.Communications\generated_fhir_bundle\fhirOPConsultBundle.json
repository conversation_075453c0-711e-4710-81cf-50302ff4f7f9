{"resourceType": "Bundle", "id": "8d23cfe8-af4e-4b62-8b35-a535d5da0497", "meta": {"versionId": "1", "lastUpdated": "2025-06-04T07:10:24.276+05:30", "profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/DocumentBundle"], "security": [{"system": "http://terminology.hl7.org/CodeSystem/v3-Confidentiality", "code": "V", "display": "very restricted"}]}, "identifier": {"system": "https://www.healtether.com", "value": "SBX_003515"}, "type": "document", "timestamp": "2025-06-04T07:10:24.276+05:30", "entry": [{"fullUrl": "urn:uuid:6795d0ab-59cd-4a57-a7ee-bc06bd8125a4", "resource": {"resourceType": "Composition", "id": "6795d0ab-59cd-4a57-a7ee-bc06bd8125a4", "meta": {"versionId": "1", "lastUpdated": "2025-06-04T07:10:24.276+05:30", "profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/OPConsultRecord"]}, "identifier": {"system": "https://www.healtether.com", "value": "SBX_003515"}, "language": "en", "status": "final", "type": {"coding": [{"system": "http://snomed.info/sct", "code": "371530004", "display": "Clinical consultation report"}], "text": "Clinical consultation report"}, "date": "2025-06-04T07:10:24.276+05:30", "title": "Consultation Report", "subject": {"reference": "urn:uuid:2f910677-e290-4644-9b51-31b9fd34550d", "display": "Patient"}, "encounter": {"reference": "urn:uuid:5f78d24d-c1b6-45c2-8a0d-99d863354442", "display": "Encounter"}, "author": [{"reference": "urn:uuid:4e06c355-2bd2-43df-bd30-67c9396fb7ec", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "attester": [{"mode": "legal", "party": {"reference": "urn:uuid:4e06c355-2bd2-43df-bd30-67c9396fb7ec", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"mode": "legal", "party": {"reference": "urn:uuid:1c8523d1-d714-4942-8be9-899b172bb066", "display": "Test"}}], "custodian": {"reference": "urn:uuid:1c8523d1-d714-4942-8be9-899b172bb066", "display": "Test"}, "section": [{"title": "Chief complaints", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "422843007", "display": "Chief complaint section"}], "text": "Chief complaint section"}, "entry": [{"reference": "urn:uuid:a78a64a5-0457-4f1f-93d5-58db088cbae5", "display": "Condition"}]}, {"title": "Allergies", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "722446000", "display": "Allergy record"}]}, "entry": [{"reference": "urn:uuid:481adb95-c373-45d4-adc2-579b95268132", "display": "AllergyIntolerance"}]}, {"title": "FamilyHistory", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "422432008", "display": "Family history section"}]}, "entry": [{"reference": "urn:uuid:3a4aecf8-a141-42ec-82bc-c4f0b08bb925", "display": "FamilyMemberHistory"}]}, {"title": "Vital Signs", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "118227000", "display": "Vital signs finding"}]}, "entry": [{"reference": "urn:uuid:cb767d57-da98-4fe6-a1c8-c6afa51edf82", "display": "Observation"}, {"reference": "urn:uuid:a26b93f0-e167-4554-8d22-7775b2583471", "display": "Observation"}, {"reference": "urn:uuid:dc5196f6-c9fa-421d-9f34-97474fc96096", "display": "Observation"}, {"reference": "urn:uuid:1a18c6d5-3677-4428-8c75-0dc8e1743b82", "display": "Observation"}, {"reference": "urn:uuid:e2c75a4d-24c5-4ff8-8ce5-3d977db2e800", "display": "Observation"}, {"reference": "urn:uuid:a49edfd4-ff0b-4ff4-9bf3-9ceb323163d5", "display": "Observation"}, {"reference": "urn:uuid:0ce4630c-1c95-4966-abf7-681e16ebc136", "display": "Observation"}, {"reference": "urn:uuid:a497fc53-42c3-405e-a85f-488d46bd1d9b", "display": "Observation"}]}, {"title": "Procedure", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "371525003", "display": "Clinical procedure report"}]}, "entry": [{"reference": "urn:uuid:cc58a861-46b1-46ca-8179-025fe3269453", "display": "Procedure"}]}, {"title": "Follow Up", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "736271009", "display": "Outpatient care plan"}]}, "entry": [{"reference": "urn:uuid:9c99f4a5-05a0-4d31-9ba2-a2a5ce719b34", "display": "Appointment"}]}, {"title": "Investigation Advice", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "721963009", "display": "Order document"}]}, "entry": [{"reference": "urn:uuid:007cddb7-25d4-43ec-ac14-e735493f7a0f", "display": "ServiceRequest"}]}, {"title": "Medications", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "721912009", "display": "Medication summary document"}]}, "entry": [{"reference": "urn:uuid:1a28f692-38d6-4eb5-b972-b86e4a0e39ea", "display": "MedicationStatement"}, {"reference": "urn:uuid:0e9a8ad7-8239-4796-b7fd-a06132196ea3", "display": "MedicationRequest"}]}, {"title": "Document Reference", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "371530004", "display": "Clinical consultation report"}]}, "entry": []}]}}, {"fullUrl": "urn:uuid:4e06c355-2bd2-43df-bd30-67c9396fb7ec", "resource": {"resourceType": "Practitioner", "id": "4e06c355-2bd2-43df-bd30-67c9396fb7ec", "meta": {"versionId": "1", "lastUpdated": "2025-06-04T07:10:24.276+05:30", "profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Practitioner"]}, "identifier": [{"type": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/v2-0203", "code": "MD", "display": "Medical License number"}]}, "system": "https://doctor.ndhm.gov.in", "value": "1234567"}], "name": [{"use": "official", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "prefix": ["Dr."]}], "telecom": [{"system": "phone", "value": "**********", "use": "mobile"}], "gender": "female", "address": [{"use": "home", "type": "physical", "text": "<PERSON><PERSON><PERSON> ganj", "postalCode": "474001", "country": "india"}]}}, {"fullUrl": "urn:uuid:1c8523d1-d714-4942-8be9-899b172bb066", "resource": {"resourceType": "Organization", "id": "1c8523d1-d714-4942-8be9-899b172bb066", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Organization"]}, "identifier": [{"type": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/v2-0203", "code": "PRN", "display": "Provider number"}]}, "system": "https://facility.ndhm.gov.in", "value": "1234567"}], "name": "Test", "telecom": [{"system": "phone", "value": "**********", "use": "work"}]}}, {"fullUrl": "urn:uuid:2f910677-e290-4644-9b51-31b9fd34550d", "resource": {"resourceType": "Patient", "id": "2f910677-e290-4644-9b51-31b9fd34550d", "meta": {"versionId": "1", "lastUpdated": "2025-06-04T07:10:24.276+05:30", "profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Patient"]}, "identifier": [{"type": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/v2-0203", "code": "MR", "display": "Medical record number"}], "text": "ABHAID"}, "system": "https://abha.abdm.gov.in/abha/v3", "value": "91-1248-5708-0632"}, {"type": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/v2-0203", "code": "MR", "display": "Medical record number"}], "text": "ABHAADDRESS"}, "system": "https://abha.abdm.gov.in/abha/v3", "value": "monika_120512@sbx"}], "name": [{"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "telecom": [{"system": "phone", "value": "**********", "use": "mobile"}], "gender": "female", "birthDate": "2003-12-05", "address": [{"use": "home", "type": "physical", "text": "nimma ji ji kho , b<PERSON><PERSON> mandi gate, jiwajiganj, Gird, Gird, Gwalior, Madhya Pradesh", "city": "GWALIOR", "state": "MADHYA PRADESH", "district": "MADHYA PRADESH", "postalCode": "474001", "country": "india"}]}}, {"fullUrl": "urn:uuid:5f78d24d-c1b6-45c2-8a0d-99d863354442", "resource": {"resourceType": "Encounter", "id": "5f78d24d-c1b6-45c2-8a0d-99d863354442", "meta": {"lastUpdated": "2025-06-04T07:10:24.276+05:30", "profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Encounter"]}, "identifier": [{"system": "https://ndhm.in", "value": "hip1"}, {"system": "https://ndhm.in", "value": "hip2"}], "status": "finished", "class": {"system": "http://terminology.hl7.org/CodeSystem/v3-ActCode", "code": "AMB", "display": "ambulatory"}, "subject": {"reference": "urn:uuid:2f910677-e290-4644-9b51-31b9fd34550d", "display": "Patient"}, "period": {"start": "2025-06-03T20:31:39.744Z", "end": "2025-06-03T20:31:39.744Z"}, "diagnosis": [{"condition": {"reference": "urn:uuid:a78a64a5-0457-4f1f-93d5-58db088cbae5", "display": "Condition"}, "use": {"coding": [{"system": "http://snomed.info/sct", "code": "67626002", "display": "Infection by Moniezia"}], "text": "(Notes: test notes)"}}]}}, {"fullUrl": "urn:uuid:481adb95-c373-45d4-adc2-579b95268132", "resource": {"resourceType": "AllergyIntolerance", "id": "481adb95-c373-45d4-adc2-579b95268132", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/AllergyIntolerance"]}, "clinicalStatus": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/allergyintolerance-clinical", "code": "active", "display": "active"}]}, "verificationStatus": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/allergyintolerance-verification", "code": "confirmed", "display": "confirmed"}]}, "code": {"coding": [{"system": "http://snomed.info/sct", "code": "75413007", "display": "Peanut"}], "text": "Peanut"}, "recordedDate": "2025-06-04T07:10:24.276+05:30", "patient": {"reference": "urn:uuid:2f910677-e290-4644-9b51-31b9fd34550d", "display": "Patient"}, "recorder": {"reference": "urn:uuid:4e06c355-2bd2-43df-bd30-67c9396fb7ec", "display": "Practitioner"}, "note": [{"text": "no notes"}]}}, {"fullUrl": "urn:uuid:3a4aecf8-a141-42ec-82bc-c4f0b08bb925", "resource": {"resourceType": "FamilyMemberHistory", "id": "3a4aecf8-a141-42ec-82bc-c4f0b08bb925", "meta": {"versionId": "1", "lastUpdated": "2025-06-04T01:40:28.851Z", "profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/FamilyMemberHistory"]}, "patient": {"reference": "urn:uuid:2f910677-e290-4644-9b51-31b9fd34550d", "type": "Patient", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "relationship": {"coding": [{"system": "http://snomed.info/sct", "code": "444018008", "display": "Person with characteristic related to subject of record"}], "text": "Person with characteristic related to subject of record"}, "status": "completed", "condition": [{"code": {"coding": [{"system": "http://snomed.info/sct", "code": "61569007", "display": "Agoraphobia without history of panic disorder (disorder)"}], "text": "Agoraphobia without history of panic disorder (disorder)"}, "note": [{"text": "test note"}], "onsetAge": {"value": 3, "unit": "Days", "system": "http://unitsofmeasure.org", "code": "a"}}]}}, {"fullUrl": "urn:uuid:cb767d57-da98-4fe6-a1c8-c6afa51edf82", "resource": {"resourceType": "Observation", "id": "cb767d57-da98-4fe6-a1c8-c6afa51edf82", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/ObservationVitalSigns"]}, "status": "final", "category": [{"coding": [{"system": "http://terminology.hl7.org/CodeSystem/observation-category", "code": "vital-signs", "display": "Vital Signs"}], "text": "Vital Signs"}], "code": {"coding": [{"system": "http://loinc.org", "code": "85354-9", "display": "Blood pressure panel with all children optional"}], "text": "Blood pressure panel with all children optional"}, "subject": {"reference": "urn:uuid:2f910677-e290-4644-9b51-31b9fd34550d", "type": "Patient", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "effectiveDateTime": "2025-06-04T07:10:24.276+05:30", "issued": "2025-06-04T01:40:28.851Z", "performer": [{"reference": "urn:uuid:4e06c355-2bd2-43df-bd30-67c9396fb7ec", "type": "Practitioner", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"reference": "urn:uuid:1c8523d1-d714-4942-8be9-899b172bb066", "type": "Organization", "display": "Test"}], "component": [{"code": {"coding": [{"system": "http://loinc.org", "code": "8462-4", "display": "Diastolic blood pressure"}], "text": "Diastolic blood pressure"}, "valueQuantity": {"value": 30, "unit": "mmhg", "system": "http://unitsofmeasure.org", "code": "mm[Hg]"}}, {"code": {"coding": [{"system": "http://loinc.org", "code": "8480-6", "display": "Systolic blood pressure"}], "text": "Systolic blood pressure"}, "valueQuantity": {"value": 120, "unit": "mmhg", "system": "http://unitsofmeasure.org", "code": "mm[Hg]"}}]}}, {"fullUrl": "urn:uuid:a26b93f0-e167-4554-8d22-7775b2583471", "resource": {"resourceType": "Observation", "id": "a26b93f0-e167-4554-8d22-7775b2583471", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/ObservationVitalSigns"]}, "status": "final", "category": [{"coding": [{"system": "http://terminology.hl7.org/CodeSystem/observation-category", "code": "vital-signs", "display": "Vital Signs"}], "text": "Vital Signs"}], "code": {"coding": [{"system": "http://loinc.org", "code": "8302-2", "display": "Body height"}], "text": "Body height"}, "subject": {"reference": "urn:uuid:2f910677-e290-4644-9b51-31b9fd34550d", "type": "Patient", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "effectiveDateTime": "2025-06-04T07:10:24.276+05:30", "issued": "2025-06-04T01:40:28.851Z", "performer": [{"reference": "urn:uuid:4e06c355-2bd2-43df-bd30-67c9396fb7ec", "type": "Practitioner", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"reference": "urn:uuid:1c8523d1-d714-4942-8be9-899b172bb066", "type": "Organization", "display": "Test"}], "valueQuantity": {"value": 130, "unit": "cm", "system": "http://unitsofmeasure.org", "code": "cm"}}}, {"fullUrl": "urn:uuid:dc5196f6-c9fa-421d-9f34-97474fc96096", "resource": {"resourceType": "Observation", "id": "dc5196f6-c9fa-421d-9f34-97474fc96096", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/ObservationVitalSigns"]}, "status": "final", "category": [{"coding": [{"system": "http://terminology.hl7.org/CodeSystem/observation-category", "code": "vital-signs", "display": "Vital Signs"}], "text": "Vital Signs"}], "code": {"coding": [{"system": "http://snomed.info/sct", "code": "27113001", "display": "Body weight"}], "text": "Body weight"}, "subject": {"reference": "urn:uuid:2f910677-e290-4644-9b51-31b9fd34550d", "type": "Patient", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "effectiveDateTime": "2025-06-04T07:10:24.276+05:30", "issued": "2025-06-04T01:40:28.851Z", "performer": [{"reference": "urn:uuid:4e06c355-2bd2-43df-bd30-67c9396fb7ec", "type": "Practitioner", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"reference": "urn:uuid:1c8523d1-d714-4942-8be9-899b172bb066", "type": "Organization", "display": "Test"}], "valueQuantity": {"value": 50, "unit": "kg", "system": "http://unitsofmeasure.org", "code": "kg"}}}, {"fullUrl": "urn:uuid:1a18c6d5-3677-4428-8c75-0dc8e1743b82", "resource": {"resourceType": "Observation", "id": "1a18c6d5-3677-4428-8c75-0dc8e1743b82", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/ObservationVitalSigns"]}, "status": "final", "category": [{"coding": [{"system": "http://terminology.hl7.org/CodeSystem/observation-category", "code": "vital-signs", "display": "Vital Signs"}], "text": "Vital Signs"}], "code": {"coding": [{"system": "http://loinc.org", "code": "61008-9", "display": "Body surface temperature"}], "text": "Body surface temperature"}, "subject": {"reference": "urn:uuid:2f910677-e290-4644-9b51-31b9fd34550d", "type": "Patient", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "effectiveDateTime": "2025-06-04T07:10:24.276+05:30", "issued": "2025-06-04T01:40:28.851Z", "performer": [{"reference": "urn:uuid:4e06c355-2bd2-43df-bd30-67c9396fb7ec", "type": "Practitioner", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"reference": "urn:uuid:1c8523d1-d714-4942-8be9-899b172bb066", "type": "Organization", "display": "Test"}], "valueQuantity": {"value": 34, "unit": "degF", "system": "http://unitsofmeasure.org", "code": "[degF]"}}}, {"fullUrl": "urn:uuid:e2c75a4d-24c5-4ff8-8ce5-3d977db2e800", "resource": {"resourceType": "Observation", "id": "e2c75a4d-24c5-4ff8-8ce5-3d977db2e800", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/ObservationVitalSigns"]}, "status": "final", "category": [{"coding": [{"system": "http://terminology.hl7.org/CodeSystem/observation-category", "code": "vital-signs", "display": "Vital Signs"}], "text": "Vital Signs"}], "code": {"coding": [{"system": "http://snomed.info/sct", "code": "78564009", "display": "Pulse rate"}], "text": "Pulse rate"}, "subject": {"reference": "urn:uuid:2f910677-e290-4644-9b51-31b9fd34550d", "type": "Patient", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "effectiveDateTime": "2025-06-04T07:10:24.276+05:30", "issued": "2025-06-04T01:40:28.851Z", "performer": [{"reference": "urn:uuid:4e06c355-2bd2-43df-bd30-67c9396fb7ec", "type": "Practitioner", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"reference": "urn:uuid:1c8523d1-d714-4942-8be9-899b172bb066", "type": "Organization", "display": "Test"}], "valueQuantity": {"value": 50, "unit": "beats/min", "system": "http://unitsofmeasure.org", "code": "/min"}}}, {"fullUrl": "urn:uuid:a49edfd4-ff0b-4ff4-9bf3-9ceb323163d5", "resource": {"resourceType": "Observation", "id": "a49edfd4-ff0b-4ff4-9bf3-9ceb323163d5", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/ObservationVitalSigns"]}, "status": "final", "category": [{"coding": [{"system": "http://terminology.hl7.org/CodeSystem/observation-category", "code": "vital-signs", "display": "Vital Signs"}], "text": "Vital Signs"}], "code": {"coding": [{"system": "http://snomed.info/sct", "code": "86290005", "display": "Respiratory rate"}], "text": "Respiratory rate"}, "subject": {"reference": "urn:uuid:2f910677-e290-4644-9b51-31b9fd34550d", "type": "Patient", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "effectiveDateTime": "2025-06-04T07:10:24.276+05:30", "issued": "2025-06-04T01:40:28.851Z", "performer": [{"reference": "urn:uuid:4e06c355-2bd2-43df-bd30-67c9396fb7ec", "type": "Practitioner", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"reference": "urn:uuid:1c8523d1-d714-4942-8be9-899b172bb066", "type": "Organization", "display": "Test"}], "valueQuantity": {"value": 40, "unit": "breaths/min", "system": "http://unitsofmeasure.org", "code": "/min"}}}, {"fullUrl": "urn:uuid:0ce4630c-1c95-4966-abf7-681e16ebc136", "resource": {"resourceType": "Observation", "id": "0ce4630c-1c95-4966-abf7-681e16ebc136", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/ObservationVitalSigns"]}, "status": "final", "category": [{"coding": [{"system": "http://terminology.hl7.org/CodeSystem/observation-category", "code": "vital-signs", "display": "Vital Signs"}], "text": "Vital Signs"}], "code": {"coding": [{"system": "http://snomed.info/sct", "code": "250554003", "display": "Measurement of oxygen saturation at periphery"}], "text": "Measurement of oxygen saturation at periphery"}, "subject": {"reference": "urn:uuid:2f910677-e290-4644-9b51-31b9fd34550d", "type": "Patient", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "effectiveDateTime": "2025-06-04T07:10:24.276+05:30", "issued": "2025-06-04T01:40:28.851Z", "performer": [{"reference": "urn:uuid:4e06c355-2bd2-43df-bd30-67c9396fb7ec", "type": "Practitioner", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"reference": "urn:uuid:1c8523d1-d714-4942-8be9-899b172bb066", "type": "Organization", "display": "Test"}], "valueQuantity": {"value": 40, "unit": "%", "system": "http://unitsofmeasure.org", "code": "%"}}}, {"fullUrl": "urn:uuid:a497fc53-42c3-405e-a85f-488d46bd1d9b", "resource": {"resourceType": "Observation", "id": "a497fc53-42c3-405e-a85f-488d46bd1d9b", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/ObservationVitalSigns"]}, "status": "final", "category": [{"coding": [{"system": "http://terminology.hl7.org/CodeSystem/observation-category", "code": "vital-signs", "display": "Vital Signs"}], "text": "Vital Signs"}], "code": {"coding": [{"system": "http://snomed.info/sct", "code": "269864005", "display": "Blood glucose result"}], "text": "Blood glucose result"}, "subject": {"reference": "urn:uuid:2f910677-e290-4644-9b51-31b9fd34550d", "type": "Patient", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "effectiveDateTime": "2025-06-04T07:10:24.276+05:30", "issued": "2025-06-04T01:40:28.852Z", "performer": [{"reference": "urn:uuid:4e06c355-2bd2-43df-bd30-67c9396fb7ec", "type": "Practitioner", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"reference": "urn:uuid:1c8523d1-d714-4942-8be9-899b172bb066", "type": "Organization", "display": "Test"}], "valueQuantity": {"value": 50, "unit": "mg/dL", "system": "http://unitsofmeasure.org", "code": "mg/dL"}}}, {"fullUrl": "urn:uuid:9c99f4a5-05a0-4d31-9ba2-a2a5ce719b34", "resource": {"resourceType": "Appointment", "id": "9c99f4a5-05a0-4d31-9ba2-a2a5ce719b34", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Appointment"]}, "status": "booked", "description": "Follow-up consultation", "start": "2023-10-01T10:00:00+05:30", "end": "2023-10-01T11:00:00+05:30", "created": "2025-06-03T20:31:39.744Z", "serviceCategory": [{"coding": [{"system": "http://snomed.info/sct", "code": "11429006", "display": "Consultation"}], "text": "Consultation"}], "serviceType": [{"coding": [{"system": "http://snomed.info/sct", "code": "60132005", "display": "General"}], "text": "General"}], "appointmentType": {"coding": [{"system": "http://snomed.info/sct", "code": "11429006", "display": "Consultation"}], "text": "Consultation"}, "specialty": [{"coding": [{"system": "http://snomed.info/sct", "code": "394579002", "display": "Cardiology"}], "text": "Cardiology"}], "participant": [{"actor": {"reference": "urn:uuid:2f910677-e290-4644-9b51-31b9fd34550d", "display": "Patient"}, "status": "accepted"}, {"actor": {"reference": "urn:uuid:4e06c355-2bd2-43df-bd30-67c9396fb7ec", "display": "Practitioner"}, "status": "accepted"}], "text": {"status": "generated", "div": "<div xmlns='http://www.w3.org/1999/xhtml' lang='en' xml:lang='en'><p>Appointment Record</p></div>"}}}, {"fullUrl": "urn:uuid:a78a64a5-0457-4f1f-93d5-58db088cbae5", "resource": {"resourceType": "Condition", "id": "a78a64a5-0457-4f1f-93d5-58db088cbae5", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Condition"]}, "clinicalStatus": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/condition-clinical", "code": "active", "display": "Active"}]}, "code": {"coding": [{"system": "http://snomed.info/sct", "code": "67626002", "display": "Infection By Moniezia"}], "text": "(Notes: test notes)"}, "recordedDate": "2025-06-04T01:39:40.402Z", "onsetPeriod": {"start": "2025-06-04T01:39:40.402Z", "end": "2025-06-04T01:39:40.402Z"}, "subject": {"reference": "urn:uuid:2f910677-e290-4644-9b51-31b9fd34550d", "display": "Patient"}}}, {"fullUrl": "urn:uuid:cc58a861-46b1-46ca-8179-025fe3269453", "resource": {"resourceType": "Procedure", "id": "cc58a861-46b1-46ca-8179-025fe3269453", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Procedure"]}, "status": "completed", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "439719009", "display": "Pasteurization (procedure)"}], "text": "(Duration:3 Days) (Notes: test notes)"}, "subject": {"reference": "urn:uuid:2f910677-e290-4644-9b51-31b9fd34550d", "display": "Patient"}, "performedDateTime": "2023-10-01T10:30:00+05:30", "followUp": [{"coding": [{"system": "http://snomed.info/sct", "code": "281036007", "display": "Follow-up consultation"}], "text": "Follow-up consultation"}], "text": {"status": "generated", "div": "<div xmlns='http://www.w3.org/1999/xhtml' lang='en' xml:lang='en'><p>Procedure Record</p></div>"}}}, {"fullUrl": "urn:uuid:007cddb7-25d4-43ec-ac14-e735493f7a0f", "resource": {"resourceType": "ServiceRequest", "id": "007cddb7-25d4-43ec-ac14-e735493f7a0f", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/ServiceRequest"]}, "status": "completed", "intent": "order", "authoredOn": "2025-06-04T07:10:24.276+05:30", "category": [{"coding": [{"system": "http://snomed.info/sct", "code": "396550006", "display": "blood test"}], "text": "(Notes: test notes)"}], "code": {"coding": [{"system": "http://snomed.info/sct", "code": "15220000", "display": "Laboratory test"}], "text": "Laboratory test"}, "subject": {"reference": "urn:uuid:2f910677-e290-4644-9b51-31b9fd34550d", "display": "Patient"}, "requester": {"reference": "urn:uuid:4e06c355-2bd2-43df-bd30-67c9396fb7ec", "display": "Practitioner"}, "text": {"status": "generated", "div": "<div xmlns='http://www.w3.org/1999/xhtml' lang='en' xml:lang='en'><p>ServiceRequest Record</p></div>"}}}, {"fullUrl": "urn:uuid:1a28f692-38d6-4eb5-b972-b86e4a0e39ea", "resource": {"resourceType": "MedicationStatement", "id": "1a28f692-38d6-4eb5-b972-b86e4a0e39ea", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/MedicationStatement"]}, "status": "completed", "dateAsserted": "2025-06-04T07:10:24.276+05:30", "medicationCodeableConcept": {"coding": [{"system": "http://snomed.info/sct", "code": "134463001", "display": "Telmisartan 20 mg oral tablet"}], "text": "Telmisartan 20 mg oral tablet"}, "subject": {"reference": "urn:uuid:2f910677-e290-4644-9b51-31b9fd34550d", "display": "Patient"}}}, {"fullUrl": "urn:uuid:0e9a8ad7-8239-4796-b7fd-a06132196ea3", "resource": {"resourceType": "MedicationRequest", "id": "0e9a8ad7-8239-4796-b7fd-a06132196ea3", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/MedicationRequest"]}, "status": "active", "intent": "order", "authoredOn": "2025-06-04T01:39:40.403Z", "medicationCodeableConcept": {"coding": [{"system": "http://snomed.info/sct", "code": "2377371000189103", "display": "Parawel 250 Mg/5 Ml Oral Suspension"}], "text": "Parawel 250 Mg/5 Ml Oral Suspension"}, "subject": {"reference": "urn:uuid:2f910677-e290-4644-9b51-31b9fd34550d", "display": "Patient"}, "requester": {"reference": "urn:uuid:4e06c355-2bd2-43df-bd30-67c9396fb7ec", "display": "Practitioner"}, "reasonReference": [{"reference": "urn:uuid:a78a64a5-0457-4f1f-93d5-58db088cbae5", "display": "Condition"}], "dosageInstruction": [{"text": "test notes only"}], "reasonCode": [{"coding": [{"system": "http://snomed.info/sct", "code": "397991007", "display": "Monitoring Problem"}], "text": "(Duration: 1 Days) (Notes: test notes)"}]}}], "signature": {"type": [{"system": "urn:iso-astm:E1762-95:2013", "code": "1.2.840.10065.1.12.1.1", "display": "Author's Signature"}], "when": "2025-06-04T07:10:24.276+05:30", "who": {"reference": "urn:uuid:4e06c355-2bd2-43df-bd30-67c9396fb7ec", "display": "Practitioner"}, "sigFormat": "image/jpeg", "data": "c2lnbmF0dXJlIGRhdGEgaGVyZQ=="}}