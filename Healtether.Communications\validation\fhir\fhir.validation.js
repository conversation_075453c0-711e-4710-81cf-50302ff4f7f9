import { checkSchema, validationResult } from "express-validator";
import signatureValidator from "./common/signatureValidator.js";
import generalValidator from "./common/generalValidator.js";
import patientValidator from "./common/patientValidator.js";
import encounterValidator from "./common/encounterValidator.js";
import organizationValidator from "./common/organizationValidator.js";
import appointmentValidator from "./common/appointmentValidator.js";
import practitionerValidator from "./common/practitionersValidator.js";
import serviceValidator from "./common/serviceValidator.js";
import conditionValidator from "./common/conditionsValidator.js";
import medicationRequestValidator from "./common/medicationRequestValidator.js";
import procedureValidator from "./common/procedureValidator.js";
import medicationStatementValidator from "./common/medicationStatementValidator.js";
import documentReferencesValidator from "./common/documentReferenceValidator.js";

export const fhirValidator = async (req, res, next) => {
  try {
    const artifact = req.body.general?.artifact;
    if (artifact === "OPConsultRecord") {
      await OPConsultRecordValidation.run(req);
    }
    if (artifact === "HealthDocumentRecord") {
      await healthDocumentRecordValidation.run(req);
    }
    if (artifact === "PrescriptionRecord") {
      await prescriptionRecordValidation.run(req);
    }
    if (artifact === "WellnessRecord") {
      await wellnessRecordValidation.run(req);
    }
    if (artifact === "InvoiceRecord") {
      await invoiceRecordValidation.run(req);
    }
    if (artifact === "ImmunizationRecord") {
      await ImmunizationRecordValidation.run(req);
    }
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      console.log("Error in:", artifact);
      console.log("ERROR", errors.array());
      return res.status(400).json({ errors: errors.array() });
    }
    next();
  } catch (error) {
    console.log("errorrr", error);
    next();
  }
};

export const healthDocumentRecordValidation = checkSchema({
  // general
  ...generalValidator("HealthDocumentRecord"),

  // PATIENT
  ...patientValidator,

  // ORGANIZATION
  ...organizationValidator,

  // PRACTITIONERS
  ...practitionerValidator,

  // SIGNATURE
  ...signatureValidator,
});

export const prescriptionRecordValidation = checkSchema({
  // general
  ...generalValidator("PrescriptionRecord"),

  // PATIENT
  ...patientValidator,

  // ENCOUNTER
  ...encounterValidator,

  // ORGANIZATION
  ...organizationValidator,

  // PRACTITIONERS
  ...practitionerValidator,

  // CONDITIONS
  ...conditionValidator,

  // MEDICATION REQUESTS
  ...medicationRequestValidator,

  // PROCEDURES
  ...procedureValidator,

  // Binary
  "binary.contentType": {
    in: ["body"],
    isString: true,
    errorMessage: "binary contentType must be a string",
  },

  // signature
  ...signatureValidator,
});

export const validateDischargeSummary = checkSchema({
  // general
  "general.artifact": {
    in: ["body"],
    isString: true,
    equals: "DischargeSummaryRecord",
    errorMessage: "Artifact must be a string.",
  },
  "general.hipUrl": {
    in: ["body"],
    isURL: true,
    errorMessage: "hipUrl must be a valid URL.",
  },
  "general.hipIds": {
    in: ["body"],
    isArray: true,
    errorMessage: "hipIds must be an array.",
  },
  "general.hipIds.*": {
    in: ["body"],
    isString: true,
    errorMessage: "Each hipId must be a string.",
  },
  "general.status": {
    in: ["body"],
    isString: true,
    isIn: {
      options: [["preliminary", "final", "amended", "entered-in-error"]],
    },
    errorMessage:
      "Status must be one of preliminary , final , amended , entered-in-error",
  },
  "general.clientId": {
    in: ["body"],
    isString: true,
    errorMessage: "clientId must be a string.",
  },

  // patient
  "patient.id": {
    in: ["body"],
    isMongoId: true,
    errorMessage: "Patient ID must be a valid Mongo ID.",
  },
  "patient.abhaNumber": {
    in: ["body"],
    isString: true,
    errorMessage: "abhaNumber must be a string.",
  },
  "patient.abhaAddress": {
    in: ["body"],
    isString: true,
    errorMessage: "abhaAddress must be a string.",
  },
  "patient.name.text": {
    in: ["body"],
    isString: true,
    errorMessage: "Patient name.text must be a string.",
  },
  "patient.name.prefix": {
    in: ["body"],
    isArray: true,
    errorMessage: "prefix must be an array of strings.",
  },
  "patient.gender": {
    in: ["body"],
    isIn: {
      options: [["male", "female", "other"]],
      errorMessage: "gender must be male, female, or other.",
    },
  },
  "patient.dob": {
    in: ["body"],
    isISO8601: true,
    errorMessage: "dob must be a valid date.",
  },

  // patient.address
  "patient.address": {
    in: ["body"],
    isArray: true,
    errorMessage: "address must be an array.",
  },
  "patient.address.*.postalCode": {
    in: ["body"],
    isPostalCode: {
      options: "IN",
      errorMessage: "postalCode must be a valid Indian postal code.",
    },
  },
  "patient.address.*.text": {
    in: ["body"],
    isString: true,
    errorMessage: "address.text must be a string.",
  },

  // patient.telecom
  "patient.telecom": {
    in: ["body"],
    isArray: true,
    errorMessage: "telecom must be an array.",
  },
  "patient.telecom.*.system": {
    in: ["body"],
    isString: true,
    errorMessage: "telecom.system must be a string.",
  },
  "patient.telecom.*.value": {
    in: ["body"],
    isMobilePhone: {
      options: ["en-IN"],
      errorMessage: "telecom.value must be a valid phone number.",
    },
  },

  // encounter
  "encounter.status": {
    in: ["body"],
    isString: true,
    errorMessage: "encounter.status is required.",
  },
  "encounter.startTime": {
    in: ["body"],
    isISO8601: true,
    errorMessage: "encounter.startTime must be a valid ISO date.",
  },
  "encounter.endTime": {
    in: ["body"],
    isISO8601: true,
    errorMessage: "encounter.endTime must be a valid ISO date.",
  },

  // appointment
  "appointment.status": {
    in: ["body"],
    isString: true,
    errorMessage: "appointment.status must be a string.",
  },
  "appointment.start": {
    in: ["body"],
    isISO8601: true,
    errorMessage: "appointment.start must be a valid ISO date.",
  },

  // practitioners
  practitioners: {
    in: ["body"],
    isArray: true,
    errorMessage: "practitioners must be an array.",
  },
  "practitioners.*.names": {
    in: ["body"],
    isArray: true,
    errorMessage: "practitioner.names must be an array.",
  },
  "practitioners.*.licenses": {
    in: ["body"],
    isArray: true,
    errorMessage: "practitioner.licenses must be an array.",
  },

  // conditions
  conditions: {
    in: ["body"],
    isArray: true,
    errorMessage: "conditions must be an array.",
  },
  "conditions.*.type": {
    in: ["body"],
    isString: true,
    errorMessage: "condition.type must be a string.",
  },
  "conditions.*.recordedDate": {
    in: ["body"],
    isISO8601: true,
    errorMessage: "condition.recordedDate must be a valid ISO date.",
  },

  // medicationRequests
  medicationRequests: {
    in: ["body"],
    isArray: true,
    errorMessage: "medicationRequests must be an array.",
  },
  "medicationRequests.*.status": {
    in: ["body"],
    isString: true,
    errorMessage: "medicationRequests.status must be a string.",
  },
  "medicationRequests.*.authoredOn": {
    in: ["body"],
    isISO8601: true,
    errorMessage: "medicationRequests.authoredOn must be a valid ISO date.",
  },

  // procedures
  procedures: {
    in: ["body"],
    isArray: true,
    errorMessage: "procedures must be an array.",
  },
  "procedures.*.status": {
    in: ["body"],
    isString: true,
    errorMessage: "procedures.status must be a string.",
  },

  // signature
  "signature.who.type": {
    in: ["body"],
    isString: true,
    errorMessage: "signature.who.type must be a string",
  },
  "signature.who.name": {
    in: ["body"],
    isString: true,
    errorMessage: "signature.who.name must be a string",
  },
  "signature.sigFormat": {
    in: ["body"],
    isString: true,
    isIn: { options: [["image/jpeg", "image/png"]] },
    errorMessage: "sigFormat must be a valid image MIME type",
  },
  "signature.data": {
    in: ["body"],
    isBase64: true,
    errorMessage: "signature.data must be base64 encoded.",
  },
});

export const invoiceRecordValidation = checkSchema({
  "general.artifact": {
    in: ["body"],
    isString: true,
    errorMessage: "Artifact must be a string",
  },
  "general.hipUrl": {
    in: ["body"],
    isURL: true,
    errorMessage: "hipUrl must be a valid URL",
  },
  "general.hipIds": {
    in: ["body"],
    isArray: true,
    errorMessage: "hipIds must be an array of strings",
  },
  "general.hipIds.*": {
    isString: true,
    errorMessage: "Each hipId must be a string",
  },
  "general.status": {
    in: ["body"],
    isString: true,
    isIn: {
      options: [["preliminary", "final", "amended", "entered-in-error"]],
    },
    errorMessage:
      "Status must be one of preliminary , final , amended , entered-in-error",
  },
  "general.clientId": {
    in: ["body"],
    isString: true,
    errorMessage: "clientId must be a string",
  },

  "patient.id": {
    in: ["body"],
    isMongoId: true,
    errorMessage: "Invalid patient id",
  },
  "patient.abhaNumber": {
    in: ["body"],
    isString: true,
    errorMessage: "abhaNumber must be a string",
  },
  "patient.abhaAddress": { isString: true },
  "patient.name.text": { isString: true },
  "patient.name.prefix": { isArray: true },
  "patient.name.prefix.*": { isString: true },
  "patient.gender": { isString: true },
  "patient.dob": { isISO8601: true },

  "patient.address": { isArray: true },
  "patient.address.*.use": { isString: true },
  "patient.address.*.type": { isString: true },
  "patient.address.*.postalCode": { isString: true },
  "patient.address.*.country": { isString: true },
  "patient.address.*.district": { isString: true },
  "patient.address.*.city": { isString: true },
  "patient.address.*.state": { isString: true },
  "patient.address.*.text": { isString: true },

  "patient.doctors": {
    in: ["body"],
    isArray: true,
    errorMessage: "Doctors must be an array of strings",
  },
  "patient.doctors.*": {
    isString: true,
    errorMessage: "Each doctor name must be a string",
  },

  "patient.allergyIntolerances": {
    in: ["body"],
    isArray: true,
    errorMessage: "allergyIntolerances must be an array",
  },
  "patient.allergyIntolerances.*.type": {
    in: ["body"],
    isString: true,
    errorMessage: "Each allergyIntolerances type must be a string",
  },
  "patient.allergyIntolerances.*.clinicalStatus": {
    in: ["body"],
    isString: true,
    errorMessage: "Each allergyIntolerances clinicalStatus must be a string",
  },
  "patient.allergyIntolerances.*.verificationStatus": {
    in: ["body"],
    isString: true,
    errorMessage:
      "Each allergyIntolerances verificationStatus must be a string",
  },
  "patient.allergyIntolerances.*.notes": {
    in: ["body"],
    isArray: true,
    errorMessage: "Each allergyIntolerances notes must be an array",
  },
  "patient.allergyIntolerances.*.notes.*": {
    in: ["body"],
    isString: true,
    errorMessage: "Each allergyIntolerances notes entry must be a string",
  },
  "patient.allergyIntolerances.*.doctor": {
    in: ["body"],
    isString: true,
    errorMessage: "Each allergyIntolerances doctor must be a string",
  },

  "patient.telecom": {
    in: ["body"],
    isArray: true,
    errorMessage: "telecom must be an array",
  },
  "patient.telecom.*.system": {
    in: ["body"],
    isString: true,
    errorMessage: "Each telecom system must be a string",
  },
  "patient.telecom.*.value": {
    in: ["body"],
    isString: true,
    errorMessage: "Each telecom value must be a string",
  },
  "patient.telecom.*.use": {
    in: ["body"],
    isString: true,
    errorMessage: "Each telecom use must be a string",
  },

  "encounter.status": { isString: true },
  "encounter.startTime": { isISO8601: true },
  "encounter.endTime": { isISO8601: true },

  "organization.name": { isString: true },
  "organization.telecom": { isArray: true },
  "organization.telecom.*.system": { isString: true },
  "organization.telecom.*.value": { isString: true },
  "organization.telecom.*.use": { isString: true },
  "organization.licenses": { isArray: true },
  "organization.licenses.*.code": { isString: true },
  "organization.licenses.*.display": { isString: true },
  "organization.licenses.*.licNo": { isString: true },

  practitioners: { isArray: true },
  "practitioners.*.names": { isArray: true },
  "practitioners.*.names.*": { isString: true },
  "practitioners.*.licenses": { isArray: true },
  "practitioners.*.licenses.*.code": { isString: true },
  "practitioners.*.licenses.*.display": { isString: true },
  "practitioners.*.licenses.*.licNo": { isString: true },
  "practitioners.*.telecom": { isArray: true },
  "practitioners.*.telecom.*.system": { isString: true },
  "practitioners.*.telecom.*.value": { isString: true },
  "practitioners.*.telecom.*.use": { isString: true },
  "practitioners.*.gender": { isString: true },
  "practitioners.*.birthDate": { isISO8601: true },
  "practitioners.*.patient": { isString: true },
  "practitioners.*.address": { isArray: true },
  "practitioners.*.address.*.use": { isString: true },
  "practitioners.*.address.*.type": { isString: true },
  "practitioners.*.address.*.postalCode": { isString: true },
  "practitioners.*.address.*.country": { isString: true },
  "practitioners.*.address.*.district": { isString: true },
  "practitioners.*.address.*.city": { isString: true },
  "practitioners.*.address.*.state": { isString: true },
  "practitioners.*.address.*.text": { isString: true },

  // conditions: { isArray: true },
  // "conditions.*.type": { isString: true },
  // "conditions.*.status": { isString: true },
  // "conditions.*.recordedDate": { isISO8601: true },
  // "conditions.*.startDate": { isISO8601: true },
  // "conditions.*.endDate": { isISO8601: true },

  // medicationRequests: { isArray: true },
  // "medicationRequests.*.status": { isString: true },
  // "medicationRequests.*.intent": { isString: true },
  // "medicationRequests.*.authoredOn": { isISO8601: true },
  // "medicationRequests.*.medication": { isString: true },
  // "medicationRequests.*.forCondition": { isArray: true },
  // "medicationRequests.*.forCondition.*": { isString: true },
  // "medicationRequests.*.reason": { isArray: true },
  // "medicationRequests.*.reason.*": { isString: true },
  // "medicationRequests.*.dosageInstruction": { isArray: true },
  // "medicationRequests.*.dosageInstruction.*.text": { isString: true },
  // "medicationRequests.*.dosageInstruction.*.repeat.frequency": {
  //   isString: true,
  // },
  // "medicationRequests.*.dosageInstruction.*.repeat.period": { isNumeric: true },
  // "medicationRequests.*.dosageInstruction.*.repeat.periodUnit": {
  //   isString: true,
  // },
  // "medicationRequests.*.dosageInstruction.*.route": { isString: true },
  // "medicationRequests.*.dosageInstruction.*.doseQuantity.value": {
  //   isString: true,
  // },
  // "medicationRequests.*.dosageInstruction.*.doseQuantity.unit": {
  //   isString: true,
  // },
  // "medicationRequests.*.dosageInstruction.*.site": { isString: true },
  // "medicationRequests.*.dosageInstruction.*.additionalInstruction": {
  //   isString: true,
  // },

  // procedures: { isArray: true },
  // "procedures.*.status": { isString: true },
  // "procedures.*.type": { isString: true },
  // "procedures.*.performedDateTime": { isString: true }, // Consider using isISO8601 if it's a date
  // "procedures.*.followUp": { isArray: true },
  // "procedures.*.followUp.*": { isString: true },

  // Binary
  // "binary.contentType": {
  //   in: ["body"],
  //   isString: true,
  //   errorMessage: "binary contentType must be a string",
  // },

  // Signature validation
  "signature.who.type": {
    in: ["body"],
    isString: true,
    errorMessage: "signature.who.type must be a string",
  },
  "signature.who.name": {
    in: ["body"],
    isString: true,
    errorMessage: "signature.who.name must be a string",
  },
  "signature.sigFormat": {
    in: ["body"],
    isString: true,
    isIn: { options: [["image/jpeg", "image/png"]] },
    errorMessage: "sigFormat must be a valid image MIME type",
  },
  "signature.data": {
    in: ["body"],
    isBase64: true,
    errorMessage: "signature data must be base64 encoded",
  },

  // New chargeItems validation
  chargeItems: {
    isArray: true,
    errorMessage: "chargeItems must be an array",
  },
  "chargeItems.*.id": {
    isMongoId: true,
    errorMessage: "Each chargeItem id must be a valid MongoDB ID",
  },
  "chargeItems.*.type": {
    isString: true,
    errorMessage: "Each chargeItem type must be a string",
  },
  "chargeItems.*.status": {
    isString: true,
    isIn: { options: [["planned", "billed", "not-billed", "aborted"]] },
    errorMessage:
      "chargeItem status must be one of planned, billed, not-billed, aborted",
  },
  "chargeItems.*.quantity": {
    isNumeric: true,
    errorMessage: "chargeItem quantity must be a number",
  },

  // New invoice validation
  "invoice.id": {
    isMongoId: true,
    errorMessage: "Invoice id must be a valid MongoDB ID",
  },
  "invoice.status": {
    isString: true,
    isIn: {
      options: [
        ["draft", "issued", "balanced", "cancelled", "entered-in-error"],
      ],
    },
    errorMessage:
      "Invoice status must be one of draft, issued, balanced, cancelled, entered-in-error",
  },
  "invoice.date": {
    isISO8601: true,
    errorMessage: "Invoice date must be a valid ISO8601 date",
  },
  "invoice.totalNet.currency": {
    isString: true,
    errorMessage: "Invoice totalNet currency must be a string",
  },
  "invoice.totalGross.currency": {
    isString: true,
    errorMessage: "Invoice totalGross currency must be a string",
  },
  "invoice.lineItem": {
    isArray: true,
    errorMessage: "Invoice lineItems must be an array",
  },
  "invoice.lineItem.*.type": {
    isString: true,
    errorMessage: "Each lineItem type must be a string",
  },
  "invoice.lineItem.*.priceComponent": {
    isArray: true,
    errorMessage: "Each lineItem priceComponent must be an array",
  },
  "invoice.lineItem.*.priceComponent.*.type": {
    isString: true,
    isIn: {
      options: [
        ["base", "surcharge", "deduction", "discount", "tax", "informational"],
      ],
    },
    errorMessage:
      "Each priceComponent type must be one of base, surcharge, deduction, discount, tax, informational",
  },
  "invoice.lineItem.*.priceComponent.*.display": {
    optional: true,
    isString: true,
    errorMessage: "Each priceComponent display must be a string when provided",
  },
  "invoice.lineItem.*.priceComponent.*.amount.value": {
    isNumeric: true,
    errorMessage: "Each priceComponent amount value must be a number",
  },
  "invoice.lineItem.*.priceComponent.*.amount.currency": {
    isString: true,
    errorMessage: "Each priceComponent amount currency must be a string",
  },

  // Document References
  documentReferences: {
    isArray: true,
    errorMessage: "documentReferences must be an array",
  },
});

export const wellnessRecordValidation = checkSchema({
  "general.artifact": {
    in: ["body"],
    isString: true,
    errorMessage: "Artifact must be a string",
  },
  "general.hipUrl": {
    in: ["body"],
    isURL: true,
    errorMessage: "hipUrl must be a valid URL",
  },
  "general.hipIds": {
    in: ["body"],
    isArray: true,
    errorMessage: "hipIds must be an array of strings",
  },
  "general.hipIds.*": {
    isString: true,
    errorMessage: "Each hipId must be a string",
  },
  "general.status": {
    in: ["body"],
    isString: true,
    isIn: {
      options: [["preliminary", "final", "amended", "entered-in-error"]],
    },
    errorMessage:
      "Status must be one of preliminary, final, amended, entered-in-error",
  },
  "general.clientId": {
    in: ["body"],
    isString: true,
    errorMessage: "clientId must be a string",
  },

  "patient.id": {
    in: ["body"],
    isMongoId: true,
    errorMessage: "Invalid patient id",
  },
  "patient.abhaNumber": {
    in: ["body"],
    isString: true,
    errorMessage: "abhaNumber must be a string",
  },
  "patient.abhaAddress": { isString: true },
  "patient.name.text": { isString: true },
  "patient.name.prefix": { isArray: true },
  "patient.name.prefix.*": { isString: true },
  "patient.gender": { isString: true },
  "patient.dob": { isISO8601: true },

  "patient.address": { isArray: true },
  "patient.address.*.use": { isString: true },
  "patient.address.*.type": { isString: true },
  "patient.address.*.postalCode": { isString: true },
  "patient.address.*.country": { isString: true },
  "patient.address.*.district": { isString: true },
  "patient.address.*.city": { isString: true },
  "patient.address.*.state": { isString: true },
  "patient.address.*.text": { isString: true },

  "patient.doctors": {
    in: ["body"],
    isArray: true,
    errorMessage: "Doctors must be an array of strings",
  },
  "patient.doctors.*": {
    isString: true,
    errorMessage: "Each doctor name must be a string",
  },

  "patient.allergyIntolerances": {
    in: ["body"],
    isArray: true,
    errorMessage: "allergyIntolerances must be an array",
  },
  "patient.allergyIntolerances.*.type": {
    in: ["body"],
    isString: true,
    errorMessage: "Each allergyIntolerances type must be a string",
  },
  "patient.allergyIntolerances.*.clinicalStatus": {
    in: ["body"],
    isString: true,
    errorMessage: "Each allergyIntolerances clinicalStatus must be a string",
  },
  "patient.allergyIntolerances.*.verificationStatus": {
    in: ["body"],
    isString: true,
    errorMessage:
      "Each allergyIntolerances verificationStatus must be a string",
  },
  "patient.allergyIntolerances.*.notes": {
    in: ["body"],
    isArray: true,
    errorMessage: "Each allergyIntolerances notes must be an array",
  },
  "patient.allergyIntolerances.*.notes.*": {
    in: ["body"],
    isString: true,
    errorMessage: "Each allergyIntolerances notes entry must be a string",
  },
  "patient.allergyIntolerances.*.doctor": {
    in: ["body"],
    isString: true,
    errorMessage: "Each allergyIntolerances doctor must be a string",
  },

  "patient.telecom": {
    in: ["body"],
    isArray: true,
    errorMessage: "telecom must be an array",
  },
  "patient.telecom.*.system": {
    in: ["body"],
    isString: true,
    errorMessage: "Each telecom system must be a string",
  },
  "patient.telecom.*.value": {
    in: ["body"],
    isString: true,
    errorMessage: "Each telecom value must be a string",
  },
  "patient.telecom.*.use": {
    in: ["body"],
    isString: true,
    errorMessage: "Each telecom use must be a string",
  },

  practitioners: { isArray: true },
  "practitioners.*.names": { isArray: true },
  "practitioners.*.names.*": { isString: true },
  "practitioners.*.licenses": { isArray: true },
  "practitioners.*.licenses.*.code": { isString: true },
  "practitioners.*.licenses.*.display": { isString: true },
  "practitioners.*.licenses.*.licNo": { isString: true },
  "practitioners.*.telecom": { isArray: true },
  "practitioners.*.telecom.*.system": { isString: true },
  "practitioners.*.telecom.*.value": { isString: true },
  "practitioners.*.telecom.*.use": { isString: true },
  "practitioners.*.gender": { isString: true },
  "practitioners.*.birthDate": { isISO8601: true },
  "practitioners.*.patient": { isString: true },
  "practitioners.*.address": { isArray: true },
  "practitioners.*.address.*.use": { isString: true },
  "practitioners.*.address.*.type": { isString: true },
  "practitioners.*.address.*.postalCode": { isString: true },
  "practitioners.*.address.*.country": { isString: true },
  "practitioners.*.address.*.district": { isString: true },
  "practitioners.*.address.*.city": { isString: true },
  "practitioners.*.address.*.state": { isString: true },
  "practitioners.*.address.*.text": { isString: true },

  // Observations validation
  observations: {
    isArray: true,
    errorMessage: "observations must be an array",
  },

  // Blood Pressure (special structure with nested array)
  "observations.*.bloodPressure": {
    optional: true,
    isArray: true,
    errorMessage: "bloodPressure must be an array",
  },
  "observations.*.bloodPressure.*.status": {
    optional: true,
    isString: true,
    isIn: {
      options: [
        [
          "registered",
          "preliminary",
          "final",
          "amended",
          "corrected",
          "cancelled",
          "entered-in-error",
          "unknown",
        ],
      ],
    },
    errorMessage:
      "Blood pressure status must be one of the valid status values",
  },
  "observations.*.bloodPressure.*.code.code": {
    optional: true,
    isString: true,
    errorMessage: "Blood pressure code.code must be a string",
  },
  "observations.*.bloodPressure.*.code.display": {
    optional: true,
    isString: true,
    errorMessage: "Blood pressure code.display must be a string",
  },
  "observations.*.bloodPressure.*.valueQuantity.value": {
    optional: true,
    isNumeric: true,
    errorMessage: "Blood pressure valueQuantity.value must be a number",
  },
  "observations.*.bloodPressure.*.valueQuantity.unit": {
    optional: true,
    isString: true,
    errorMessage: "Blood pressure valueQuantity.unit must be a string",
  },
  "observations.*.bloodPressure.*.valueQuantity.code": {
    optional: true,
    isString: true,
    errorMessage: "Blood pressure valueQuantity.code must be a string",
  },
  "observations.*.bloodPressure.*.effectiveDateTime": {
    optional: true,
    isISO8601: true,
    errorMessage:
      "Blood pressure effectiveDateTime must be a valid ISO8601 date",
  },

  // Regular vital signs and other observations
  "observations.*.status": {
    optional: true,
    isString: true,
    isIn: {
      options: [
        [
          "registered",
          "preliminary",
          "final",
          "amended",
          "corrected",
          "cancelled",
          "entered-in-error",
          "unknown",
        ],
      ],
    },
    errorMessage: "Observation status must be one of the valid status values",
  },
  "observations.*.code.code": {
    optional: true,
    isString: true,
    errorMessage: "Observation code.code must be a string",
  },
  "observations.*.code.display": {
    optional: true,
    isString: true,
    errorMessage: "Observation code.display must be a string",
  },
  "observations.*.valueQuantity.value": {
    optional: true,
    isNumeric: true,
    errorMessage: "Observation valueQuantity.value must be a number",
  },
  "observations.*.valueQuantity.unit": {
    optional: true,
    isString: true,
    errorMessage: "Observation valueQuantity.unit must be a string",
  },
  "observations.*.valueQuantity.code": {
    optional: true,
    isString: true,
    errorMessage: "Observation valueQuantity.code must be a string",
  },
  "observations.*.effectiveDateTime": {
    optional: true,
    isISO8601: true,
    errorMessage: "Observation effectiveDateTime must be a valid ISO8601 date",
  },

  // Signature validation
  "signature.who.type": {
    in: ["body"],
    isString: true,
    errorMessage: "signature.who.type must be a string",
  },
  "signature.who.name": {
    in: ["body"],
    isString: true,
    errorMessage: "signature.who.name must be a string",
  },
  "signature.sigFormat": {
    in: ["body"],
    isString: true,
    isIn: { options: [["image/jpeg", "image/png"]] },
    errorMessage: "sigFormat must be a valid image MIME type",
  },
  "signature.data": {
    in: ["body"],
    isBase64: true,
    errorMessage: "signature data must be base64 encoded",
  },
    // ORGANIZATION
  ...organizationValidator,
});

export const OPConsultRecordValidation = checkSchema({
  // GENERAL
  ...generalValidator("OPConsultRecord"),

  // PATIENT
  ...patientValidator,

  // ENCOUNTER
  ...encounterValidator,

  // ORGANIZATION
  ...organizationValidator,

  // APPOINTMENT
  ...appointmentValidator,

  // PRACTITIONERS
  ...practitionerValidator,

  // SERVICE REQUESTS
  ...serviceValidator,

  // CONDITIONS
  ...conditionValidator,

  // MEDICATION STATEMENTS
  ...medicationStatementValidator,

  // MEDICATION REQUESTS
  ...medicationRequestValidator,

  // PROCEDURES
  ...procedureValidator,

  // SIGNATURE
  ...signatureValidator,
});

export const ImmunizationRecordValidation = checkSchema({
  // GENERAL
  ...generalValidator("ImmunizationRecord"),

  // PATIENT
  ...patientValidator,

  // PRACTITIONERS
  ...practitionerValidator,

  // ENCOUNTER
  ...encounterValidator,

  // ORGANIZATION
  ...organizationValidator,

  // IMMUNIZATION
  immunizations: {
    in: ["body"],
    isArray: {
      errorMessage: "immunizations must be an array",
    },
  },
  "immunizations.*.status": {
    in: ["body"],
    isString: true,
    notEmpty: true,
    // isIn: {
    //   options: [["completed", "in-progress", "entered-in-error"]],
    // },
    errorMessage:
      "status must be one of: completed, in-progress, entered-in-error",
  },
  "immunizations.*.type": {
    in: ["body"],
    isString: true,
    notEmpty: true,
    errorMessage: "type is required and must be a non-empty string",
  },
  "immunizations.*.occurrenceDateTime": {
    in: ["body"],
    
    errorMessage: "occurrenceDateTime must be a valid ISO 8601 date string",
  },
  "immunizations.*.primarySource": {
    in: ["body"],
    isBoolean: true,
    errorMessage: "primarySource must be a boolean",
  },
  "immunizations.*.lotNumber": {
    in: ["body"],
    isString: true,
    notEmpty: true,
    errorMessage: "lotNumber is required and must be a non-empty string",
  },
  // // immunizationRecommendations
  // immunizationRecommendations: {
  //   in: ["body"],
  //   isArray: {
  //     errorMessage: "immunizationRecommendations must be an array",
  //   },
  // },
  // "immunizationRecommendations.*.type": {
  //   in: ["body"],
  //   isString: true,
  //   notEmpty: true,
  //   errorMessage: "type is required and must be a non-empty string",
  // },
  // "immunizationRecommendations.*.date": {
  //   in: ["body"],
  //   isISO8601: true,
  //   errorMessage: "date must be a valid ISO 8601 date string",
  // },
  // "immunizationRecommendations.*.recommendation": {
  //   in: ["body"],
  //   isArray: {
  //     errorMessage: "recommendation must be an array",
  //   },
  // },
  // "immunizationRecommendations.*.recommendation.*.description": {
  //   in: ["body"],
  //   isString: true,
  //   notEmpty: true,
  //   errorMessage: "description is required and must be a non-empty string",
  // },
  // "immunizationRecommendations.*.recommendation.*.series": {
  //   in: ["body"],
  //   isString: true,
  //   notEmpty: true,
  //   errorMessage: "series is required and must be a non-empty string",
  // },
  // "immunizationRecommendations.*.recommendation.*.doseNumberPositiveInt": {
  //   in: ["body"],
  //   isInt: { options: { min: 1 } },
  //   errorMessage: "doseNumberPositiveInt must be a positive integer",
  // },
  // "immunizationRecommendations.*.recommendation.*.seriesDosesPositiveInt": {
  //   in: ["body"],
  //   isInt: { options: { min: 1 } },
  //   errorMessage: "seriesDosesPositiveInt must be a positive integer",
  // },
  // "immunizationRecommendations.*.recommendation.*.forecastStatus": {
  //   in: ["body"],
  //   isString: true,
  //   notEmpty: true,
  //   errorMessage: "forecastStatus is required and must be a non-empty string",
  // },
  // "immunizationRecommendations.*.recommendation.*.dateCriterion": {
  //   in: ["body"],
  //   isISO8601: true,
  //   errorMessage: "dateCriterion must be a valid ISO 8601 date string",
  // },

  // SIGNATURE
  ...signatureValidator,
});
