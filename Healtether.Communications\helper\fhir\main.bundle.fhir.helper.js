import dotenv from "dotenv";
import axios from "axios";
import { v4 as uuidv4 } from "uuid";
import fs from "fs";

import portalDb from "../../config/clinics.collections.config.js";
import { OPConsultRecordSchema } from "../../schema/fhir_schema/op_consult.schema.fhir.js";
import { PrescriptionRecordSchema } from "../../schema/fhir_schema/prescription.schema.fhir.js";
import { HealthDocumentRecordSchema } from "../../schema/fhir_schema/health_document.schema.fhir.js";

import { AbhaResponse } from "../../utils/abha.api.js";
import { DIAGNOSTIC_REPORT_RECORD, DISCHARGE_SUMMARY_RECORD, HEALTH_DOCUMENT_RECORD, IMMUNIZATION_RECORD, OP_CONSULT_RECORD, PRESCRIPTION_RECORD, WELLNESS_RECORD, INVOICE_RECORD, bundleMetadata, bundleIdentifier, signatureConstant, DIAGNOSTIC_REPORT_MEDIA_RECORD } from "../../utils/fhir.constants.js";
import { generatePatientResource } from "./common_resources/patient.resource.fhir.js";
import { generateEncounterResource } from "./common_resources/encounter.resource.fhir.js";
import { generateConditionResource } from "./common_resources/condition.resource.fhir.js";
import { generatePractitionerResource } from "./common_resources/practitioner.resource.fhir.js";
import { generateOrganizationResource } from "./common_resources/organization.resource.fhir.js";
import { generateAllergyIntoleranceResource } from "./common_resources/allergy_intolerance.resource.fhir.js";
import { generateServiceRequestResource } from "./op_consult_record/service_request.resource.fhir.js";
import { generateMedicationStatementResource } from "./common_resources/medication_statement.resource.fhir.js";
import { generateMedicationRequestResource } from "./common_resources/medication_request.resource.fhir.js";
import { generateProcedureResource } from "./common_resources/procedure.resource.fhir.js";
import { generateDocumentReferenceResource } from "./common_resources/document_reference.resource.fhir.js";
import { generateAppointmentResource } from "./op_consult_record/appointment.resource.fhir.js";
import { generateFamilyMemberHistoryResource } from "./common_resources/family_member_history.resource.fhir.js";
import { generateObservationResourcesFromVitals } from "./common_resources/observation.resource.fhir.js";
import { generateDiagnosticReportComposition, generateHealthDocumentComposition, generateImmunizationComposition, generateInvoiceComposition, generateOpConsultComposition, generateWellnessComposition } from "./common_resources/composition.resource.fhir.js";
import { generatePrescriptionComposition } from "./common_resources/composition.resource.fhir.js";
import { generateBinaryResource } from "./common_resources/binary.resource.fhir.js";
import { generateObservationResource } from "./common_resources/observation.resource.fhir.js";
import { DischargeSummaryRecordSchema } from "../../schema/fhir_schema/discharge_summary.schema.fhir.js";
import { generateImmunizationResource } from "./immunization_record/immunization.resource.fhir.js";
import { generateImmunizationRecommendationResource } from "./immunization_record/immunization_recommendation.resource.fhir.js"
import { generateDiagnosticReportResource } from "./common_resources/diagnostic_report_lab.resource.fhir.js";
import { generateInvoiceResource } from "./common_resources/invoice.resource.fhir.js";
import { generateChargeItemResource } from "./common_resources/chargeItem.resource.fhir.js";
import { ImmunizationRecordSchema } from "../../schema/fhir_schema/immunization.schema.fhir.js";
import { InvoiceRecordSchema } from "../../schema/fhir_schema/invoice.schema.fhir.js";
import { WellnessRecordSchema } from "../../schema/fhir_schema/wellness.schema.fhir.js";
import { DiagnosticReportSchema } from "../../schema/fhir_schema/diagnostic_report_lab.schema.fhir.js";
import path from "path";

dotenv.config();

export const handleApisComingFromClinicForFhir = async (req) => {
  try {
    // console.log(`Received request from Clinic in path: ${req.url}`);
    // console.log("Request Body:", req.body);

    const { general } = req.body
    const artifactKey = extractArtifactsKey(general.artifact);
    const handler = artifactHandlers[artifactKey] || artifactHandlers.defaultHandler;
    console.log('Update to: ', artifactKey);
    return await handler(req);
  } catch (error) {
    console.error("Error handling event:", error.message);
    throw error;
  }
}
export const handleApisComingFromAbhaAppForFhir = async (data) => {
  try {
    // console.log(`Received datauest from Clinic in path: ${data.url}`);
    // console.log("datauest Body:", data.body);
    const { general } = data.body
    const artifactKey = extractArtifactsKey(general.artifact);
    console.log(artifactKey)
    const handler = artifactHandlersForAbhaApp[artifactKey] || artifactHandlersForAbhaApp.defaultHandler;
    console.log('Update to: ', artifactKey);

    // if(artifactKey==='PrescriptionRecord')
    //     return fhirPrescriptionBundle = await forwardToPrescriptionRecordBundle(data, PRESCRIPTION_RECORD, getCurrentTimeInGMT530());
    // if(artifactKey==='HealthDocumentRecord')
    //     return fhirHealthDocumentBundle = await forwardToHealthDocumentRecordBundle(data, HEALTH_DOCUMENT_RECORD, getCurrentTimeInGMT530());
    // if(artifactKey==='WellnessRecord')
    //     return fhirWellnessBundle = await forwardToWellnessRecordBundle(data, WELLNESS_RECORD, getCurrentTimeInGMT530());
    // if(artifactKey==='InvoiceRecord')
    //     return fhirInvoiceBundle = await forwardToInvoiceRecordBundle(data, INVOICE_RECORD, getCurrentTimeInGMT530());
    // if(artifactKey==='OPConsultRecord')
    //     return fhirOPConsultBundle = await forwardToOPConsultRecordBundle(data, OP_CONSULT_RECORD, getCurrentTimeInGMT530());
    // if(artifactKey==='DiagnosticReportRecord')
    //     return fhirDiagnosticBundle = await forwardToDiagnosticReportRecordBundle(data, DIAGNOSTIC_REPORT_RECORD, getCurrentTimeInGMT530());
    // if(artifactKey==='ImmunizationRecord')
    //     return fhirImmunizationBundle = await forwardToImmunizationRecordBundle(data, IMMUNIZATION_RECORD, getCurrentTimeInGMT530());
    // if(artifactKey==='DischargeSummaryRecord')
    //     return fhirDischargeSummaryBundle = await forwardToDischargeSummaryRecordBundle(data, DISCHARGE_SUMMARY_RECORD, getCurrentTimeInGMT530());

    // console.log("data",data)

    return await handler(data);
  } catch (error) {
    console.error("Error handling event:", error.message);
    throw error;
  }
}

function extractArtifactsKey(artifact) {
  if (artifact.toLowerCase().includes(DIAGNOSTIC_REPORT_RECORD.toLowerCase())) return 'generateDiagnosticReportRecordBundle';
  if (artifact.toLowerCase().includes(DISCHARGE_SUMMARY_RECORD.toLowerCase())) return 'generateDischargeSummaryRecordBundle';
  if (artifact.toLowerCase().includes(HEALTH_DOCUMENT_RECORD.toLowerCase())) return 'generateHealthDocumentRecordBundle';
  if (artifact.toLowerCase().includes(IMMUNIZATION_RECORD.toLowerCase())) return 'generateImmunizationRecordBundle';
  if (artifact.toLowerCase().includes(OP_CONSULT_RECORD.toLowerCase())) return 'generateOPConsultRecordBundle';
  if (artifact.toLowerCase().includes(PRESCRIPTION_RECORD.toLowerCase())) return 'generatePrescriptionRecordBundle';
  if (artifact.toLowerCase().includes(WELLNESS_RECORD.toLowerCase())) return 'generateWellnessRecordBundle';
  if (artifact.toLowerCase().includes(INVOICE_RECORD.toLowerCase())) return 'generateInvoiceRecordBundle';
  return 'defaultHandler';
}

const artifactHandlers = {


  generateDiagnosticReportRecordBundle: async (req) => {
    try {
      const fhirId = uuidv4();

      if (req.query.skipFhirProcessing) {

        const DiagnosticReportModel = portalDb.model("DiagnosticReportFHIRRecord", DiagnosticReportSchema);
        const body = req.body;
        const diagnosticReport = new DiagnosticReportModel({
          fhirId,
          general: body.general,
          patient: body.patient,
          encounter: body.encounter,
          practitioners: body.practitioners,
          organization: body.organization,
          diagnosticReports: body.diagnosticReports,
          signature: body.signature
        });

        let result = await diagnosticReport.save();
        return new AbhaResponse(true, { bundle: result, fhirId });
      }
      const fhirDiagnosticBundle = await forwardToDiagnosticReportRecordBundle(req, DIAGNOSTIC_REPORT_RECORD, getCurrentTimeInGMT530());

      // const compositionEntry = fhirDiagnosticBundle.entry.find(entry => entry.resource.resourceType === "Composition");
      // if (!compositionEntry) {
      //     throw new Error("No Composition resource found in the FHIR bundle");
      // }
      // const compositionId = compositionEntry.resource.id;
      // // console.log("Diagnostic Report FHIR Bundle: ", JSON.stringify(fhirDiagnosticBundle, null, 2));
      // // console.log("Record saved successfully:", fhirId);

      return new AbhaResponse(true, { bundle: fhirDiagnosticBundle, fhirId });
    } catch (error) {
      console.error(error);
      return new AbhaResponse(false, {
        message: "An error occurred while processing the request",
        error: error,
      });
    }
  },

  generateDischargeSummaryRecordBundle: async (req) => {
    try {
      const fhirId = uuidv4();
      if (req.query.skipFhirProcessing) {

        const DischargeSummaryRecordModel = portalDb.model("DischargeSummaryFHIRRecord", DischargeSummaryRecordSchema);
        const body = req.body;
        const record = new DischargeSummaryRecordModel({
          fhirId,
          general: body.general,
          patient: body.patient,
          practitioners: body.practitioners,
          encounter: body.encounter,
          organization: body.organization,
          conditions: body.conditions,
          serviceRequests: body.serviceRequests,
          medicationStatements: body.medicationStatements,
          medicationRequests: body.medicationRequests,
          procedures: body.procedures,
          appointment: body.appointment,
          signature: body.signature,
          dischargeSummary: body.dischargeSummary
        });

        let result = await record.save();
        return new AbhaResponse(true, { bundle: result, fhirId });
      } else {
        const fhirDischargeSummaryBundle = await forwardToDischargeSummaryRecordBundle(req, DISCHARGE_SUMMARY_RECORD, getCurrentTimeInGMT530());

        // const compositionEntry = fhirDischargeSummaryBundle.entry.find(entry => entry.resource.resourceType === "Composition");
        // if (!compositionEntry) {
        //     throw new Error("No Composition resource found in the FHIR bundle");
        // }
        // const compositionId = compositionEntry.resource.id;
        // const fhirId = `${compositionId}`;
        return new AbhaResponse(true, { bundle: fhirDischargeSummaryBundle, fhirId });
      }
    } catch (error) {
      console.error(error);
      return new AbhaResponse(false, {
        message: "An error occurred while processing the request",
        error: error,
      });
    }
  },


  generateHealthDocumentRecordBundle: async (req) => {
    try {

      const fhirId = uuidv4();
      if (req.query.skipFhirProcessing) {
        const HealthDocumentRecordModel = portalDb.model("HealthDocumentFHIRRecord", HealthDocumentRecordSchema);

        const body = req.body;
        const healthDocument = new HealthDocumentRecordModel({
          fhirId,
          general: body.general,
          patient: body.patient,
          practitioners: body.practitioners,
          organization: body.organization,
          documentReferences: body.documentReferences,
          signature: body.signature
        });
        let result = await healthDocument.save();

        return new AbhaResponse(true, { bundle: result, fhirId });
      } else {
        const fhirHealthDocumentBundle = await forwardToHealthDocumentRecordBundle(req, HEALTH_DOCUMENT_RECORD, getCurrentTimeInGMT530());

        // const compositionEntry = fhirHealthDocumentBundle.entry.find(entry => entry.resource.resourceType === "Composition");
        // if (!compositionEntry) {
        //     throw new Error("No Composition resource found in the FHIR bundle");
        // }
        // const compositionId = compositionEntry.resource.id;
        // const fhirId = `${compositionId}`;
        return new AbhaResponse(true, { bundle: fhirHealthDocumentBundle, fhirId });
      }
    } catch (error) {
      console.error(error);
      return new AbhaResponse(false, {
        message: "An error occurred while processing the request",
        error: error,
      });
    }
  },

  generateImmunizationRecordBundle: async (req) => {
    try {
      const fhirId = uuidv4();
      if (req.query.skipFhirProcessing) {
        const ImmunizationRecordModel = portalDb.model("ImmunizationReportFHIRRecord", ImmunizationRecordSchema);
        const body = req.body;
        const immunization = new ImmunizationRecordModel({
          fhirId,
          general: body.general,
          patient: body.patient,
          encounter: body.encounter,
          practitioners: body.practitioners,
          organization: body.organization,
          immunizations: body.immunizations,
          signature: body.signature
        });

        let result = await immunization.save();
        return new AbhaResponse(true, { bundle: result, fhirId });
      } else {
        const fhirImmunizationBundle = await forwardToImmunizationRecordBundle(req, IMMUNIZATION_RECORD, getCurrentTimeInGMT530());

        // const compositionEntry = fhirImmunizationBundle.entry.find(entry => entry.resource.resourceType === "Composition");
        // if (!compositionEntry) {
        //     throw new Error("No Composition resource found in the FHIR bundle");
        // }
        // const compositionId = compositionEntry.resource.id;
        // const fhirId = `${compositionId}`;
        return new AbhaResponse(true, { bundle: fhirImmunizationBundle, fhirId });
      }
    } catch (error) {
      console.error(error);
      return new AbhaResponse(false, {
        message: "An error occurred while processing the request",
        error: error,
      });
    }
  },



  generateOPConsultRecordBundle: async (req) => {
    try {

      const fhirId = uuidv4();
      if (req.query.skipFhirProcessing) {
        const OPConsultRecordModel = portalDb.model("OPConsultFHIRRecord", OPConsultRecordSchema);
        const body = req.body;
        const opConsult = new OPConsultRecordModel({
          fhirId,
          general: body.general,
          patient: body.patient,
          practitioners: body.practitioners,
          encounter: body.encounter,
          organization: body.organization,
          conditions: body.conditions,
          serviceRequests: body.serviceRequests,
          medicationStatements: body.medicationStatements,
          medicationRequests: body.medicationRequests,
          familyHistory: body.familyHistory,
          vitals: body.vitals,
          procedures: body.procedures,
          documentReferences: body.documentReferences,
          appointment: body.appointment,
          signature: body.signature
        });
        let result = await opConsult.save();
        return new AbhaResponse(true, { bundle: result, fhirId });
      }
      else {
        const fhirOPConsultBundle = await forwardToOPConsultRecordBundle(req, OP_CONSULT_RECORD, getCurrentTimeInGMT530());
        return new AbhaResponse(true, { bundle: fhirOPConsultBundle, fhirId });

        // const compositionEntry = fhirOPConsultBundle.entry.find(entry => entry.resource.resourceType === "Composition");
        // if (!compositionEntry) {
        //     throw new Error("No Composition resource found in the FHIR bundle");
        // }
        // const compositionId = compositionEntry.resource.id;
        // const fhirId = `${compositionId}`;
        return new AbhaResponse(true, { bundle: fhirOPConsultBundle, fhirId });
      }

    } catch (error) {
      console.error(error);
      return new AbhaResponse(false, {
        message: "An error occurred while processing the request",
        error: error,
      });
    }
  },

  generatePrescriptionRecordBundle: async (req) => {
    try {

      const fhirId = uuidv4();
      if (req.query.skipFhirProcessing) {
        const PrescriptionRecordModel = portalDb.model("PrescriptionFHIRRecord", PrescriptionRecordSchema);
        const body = req.body;
        const prescription = new PrescriptionRecordModel({
          fhirId,
          general: body.general,
          patient: body.patient,
          encounter: body.encounter,
          practitioners: body.practitioners,
          conditions: body.conditions,
          organization: body.organization,
          medicationRequests: body.medicationRequests,
          binary: body.binary,
          signature: body.signature
        });
        let result = await prescription.save();
        return new AbhaResponse(true, { bundle: result, fhirId });
      }
      else {
        const fhirPrescriptionBundle = await forwardToPrescriptionRecordBundle(req, PRESCRIPTION_RECORD, getCurrentTimeInGMT530());
        return new AbhaResponse(true, { bundle: fhirPrescriptionBundle, fhirId });
      }

    } catch (error) {
      console.error(error);
      return new AbhaResponse(false, {
        message: "An error occurred while processing the request",
        error: error,
      });
    }
  },

  generateWellnessRecordBundle: async (req) => {
    try {
      const fhirId = uuidv4();
      if (req.query.skipFhirProcessing) {
        const WellnessRecordModel = portalDb.model("WellnessReportFHIRRecord", WellnessRecordSchema);
        const body = req.body;
        const wellnessRecord = new WellnessRecordModel({
          fhirId,
          general: body.general,
          patient: body.patient,
          practitioners: body.practitioners,
          // encounter: body.encounter,
          // organization: body.organization,
          observations: body.observations,
          signature: body.signature
        });
        let result = await wellnessRecord.save();
        return new AbhaResponse(true, { bundle: result, fhirId });
      }
      else {
        const fhirWellnessBundle = await forwardToWellnessRecordBundle(req, WELLNESS_RECORD, getCurrentTimeInGMT530());

        return new AbhaResponse(true, { bundle: fhirWellnessBundle, fhirId });
      }
    } catch (error) {
      console.error(error);
      return new AbhaResponse(false, {
        message: "An error occurred while processing the request",
        error: error,
      });
    }
  },

  generateInvoiceRecordBundle: async (req) => {
    try {
      const fhirId = uuidv4();
      if (req.query.skipFhirProcessing) {
        const InvoiceRecordModel = portalDb.model("InvoiceReportFHIRRecord", InvoiceRecordSchema);
        const body = req.body;
        const invoice = new InvoiceRecordModel({
          fhirId,
          general: body.general,
          patient: body.patient,
          practitioners: body.practitioners,
          encounter: body.encounter,
          organization: body.organization,
          invoice: body.invoice,
          chargeItems: body.chargeItems,
          binary: body.binary,
          signature: body.signature
        });
        let result = await invoice.save();
        return new AbhaResponse(true, { bundle: result, fhirId });
      } else {
        const fhirInvoiceBundle = await forwardToInvoiceRecordBundle(req, INVOICE_RECORD, getCurrentTimeInGMT530());
        return new AbhaResponse(true, { bundle: fhirInvoiceBundle, fhirId });
      }

    } catch (error) {
      console.error(error);
      return new AbhaResponse(false, {
        message: "An error occurred while processing the request",
        error: error,
      });
    }
  },


  defaultHandler: async () => {
    throw {
      status: 404,
      data: { error: { message: "Unsupported Artifact" } },
    };
  }
}
const artifactHandlersForAbhaApp = {


  generateDiagnosticReportRecordBundle: async (req) => {
    try {
      const fhirDiagnosticBundle = await forwardToDiagnosticReportRecordBundle(req, DIAGNOSTIC_REPORT_RECORD, getCurrentTimeInGMT530());
      fs.writeFileSync(path.join("./generated_fhir_bundle", "fhirDiagnosticBundle.json"), JSON.stringify(fhirDiagnosticBundle, null, 2), "utf-8");
      return fhirDiagnosticBundle;

    } catch (error) {
      console.error(error);
      return new AbhaResponse(false, {
        message: "An error occurred while processing the request",
        error: error,
      });
    }
  },

  generateDischargeSummaryRecordBundle: async (req) => {
    try {
      const fhirDischargeSummaryBundle = await forwardToDischargeSummaryRecordBundle(req, DISCHARGE_SUMMARY_RECORD, getCurrentTimeInGMT530());
      fs.writeFileSync(path.join("./generated_fhir_bundle", "fhirDischargeSummaryBundle.json"), JSON.stringify(fhirDischargeSummaryBundle, null, 2), "utf-8");
      return fhirDischargeSummaryBundle;
    } catch (error) {
      console.error(error);
      return new AbhaResponse(false, {
        message: "An error occurred while processing the request",
        error: error,
      });
    }
  },


  generateHealthDocumentRecordBundle: async (req) => {
    try {
      const fhirHealthDocumentBundle = await forwardToHealthDocumentRecordBundle(req, HEALTH_DOCUMENT_RECORD, getCurrentTimeInGMT530());
      fs.writeFileSync(path.join("./generated_fhir_bundle", "fhirHealthDocumentBundle.json"), JSON.stringify(fhirHealthDocumentBundle, null, 2), "utf-8");
      return fhirHealthDocumentBundle;
    } catch (error) {
      console.error(error);
      return new AbhaResponse(false, {
        message: "An error occurred while processing the request",
        error: error,
      });
    }
  },

  generateImmunizationRecordBundle: async (req) => {
    try {
      const fhirImmunizationBundle = await forwardToImmunizationRecordBundle(req, IMMUNIZATION_RECORD, getCurrentTimeInGMT530());
      fs.writeFileSync(path.join("./generated_fhir_bundle", "fhirImmunizationBundle.json"), JSON.stringify(fhirImmunizationBundle, null, 2), "utf-8");
      return fhirImmunizationBundle;


      // console.log("Immunization Record FHIR Bundle: ", JSON.stringify(fhirImmunizationBundle, null, 2));
      // console.log("Record saved successfully:", fhirId);

      // return new AbhaResponse(true, {bundle:fhirImmunizationBundle,fhirId});
    } catch (error) {
      console.error(error);
      return new AbhaResponse(false, {
        message: "An error occurred while processing the request",
        error: error,
      });
    }
  },



  generateOPConsultRecordBundle: async (req) => {
    try {
      const fhirOPConsultBundle = await forwardToOPConsultRecordBundle(req, OP_CONSULT_RECORD, getCurrentTimeInGMT530());
      fs.writeFileSync(path.join("./generated_fhir_bundle", "fhirOPConsultBundle.json"), JSON.stringify(fhirOPConsultBundle, null, 2), "utf-8");

      return fhirOPConsultBundle;
    } catch (error) {
      console.error(error);
      return new AbhaResponse(false, {
        message: "An error occurred while processing the request",
        error: error,
      });
    }
  },

  generatePrescriptionRecordBundle: async (req) => {
    try {
      const fhirPrescriptionBundle = await forwardToPrescriptionRecordBundle(req, PRESCRIPTION_RECORD, getCurrentTimeInGMT530());
      fs.writeFileSync(path.join("./generated_fhir_bundle", "fhirPrescriptionBundle.json"), JSON.stringify(fhirPrescriptionBundle, null, 2), "utf-8");
      return fhirPrescriptionBundle;
    } catch (error) {
      console.error(error);
      return new AbhaResponse(false, {
        message: "An error occurred while processing the request",
        error: error,
      });
    }
  },

  generateWellnessRecordBundle: async (req) => {
    try {
      const fhirWellnessBundle = await forwardToWellnessRecordBundle(req, WELLNESS_RECORD, getCurrentTimeInGMT530());
      fs.writeFileSync(path.join("./generated_fhir_bundle", "fhirWellnessBundle.json"), JSON.stringify(fhirWellnessBundle, null, 2), "utf-8");
      return fhirWellnessBundle;

      // const filePath = path.join("./", "fhirWellnessBundle.json");


      // Save JSON data to file
      // fs.writeFileSync(filePath, JSON.stringify(fhirWellnessBundle, null, 2), "utf-8");
      // console.log("Wellness Record FHIR Bundle: ", JSON.stringify(fhirWellnessBundle, null, 2));
      // console.log("Record saved successfully:", fhirId);

      // return new AbhaResponse(true, {bundle:fhirWellnessBundle,fhirId:fhirId});
    } catch (error) {
      console.error(error);
      return new AbhaResponse(false, {
        message: "An error occurred while processing the request",
        error: error,
      });
    }
  },

  generateInvoiceRecordBundle: async (req) => {
    try {
      const fhirInvoiceBundle = await forwardToInvoiceRecordBundle(req, INVOICE_RECORD, getCurrentTimeInGMT530());
      fs.writeFileSync(path.join("./generated_fhir_bundle", "fhirInvoiceBundle.json"), JSON.stringify(fhirInvoiceBundle, null, 2), "utf-8");
      return fhirInvoiceBundle;


      // console.log("__dirname:", __dirname);
      // const filePath = path.join("./", "fhirInvoiceBundle.json");


      // Save JSON data to file
      // fs.writeFileSync(filePath, JSON.stringify(fhirInvoiceBundle, null, 2), "utf-8");
      // console.log("Invoice Record FHIR Bundle: ", JSON.stringify(fhirInvoiceBundle, null, 2));
      // console.log("Record saved successfully:", fhirId);

      // return new AbhaResponse(true, {bundle:fhirInvoiceBundle,fhirId});
    } catch (error) {
      console.error(error);
      return new AbhaResponse(false, {
        message: "An error occurred while processing the request",
        error: error,
      });
    }
  },


  defaultHandler: async () => {
    throw {
      status: 404,
      data: { error: { message: "Unsupported Artifact" } },
    };
  }
}

const getCurrentTimeInGMT530 = () => {
  const now = new Date();
  const offset = 5.5 * 60;
  const timezoneDate = new Date(now.getTime() + offset * 60 * 1000);
  return timezoneDate.toISOString().replace('Z', '+05:30');
};

// Removed global variables to prevent race conditions between concurrent requests
// Each function now uses local variables to ensure request isolation

const createResourceArrays = () => {
  return {
    conditionResources: [],
    practitionerResources: [],
    allergyIntoleranceResources: [],
    serviceRequestResources: [],
    medicationStatementResources: [],
    medicationRequestResources: [],
    procedureResources: [],
    documentReferenceResources: [],
    dischargeSummaryResources: [],
    familyMemberHistoryResources: [],
    immunizationRecommendationResources: [],
    immunizationResources: [],
    observationResources: [],
    diagnosticReportResources: [],
    chargeItemResources: []
  };
};


const forwardToInvoiceRecordBundle = async (req, bundleId, currentTime) => {
  try {
    // Create local resource arrays to prevent race conditions
    const resources = createResourceArrays();
    let patientResource;
    let encounterResource;
    let organizationResource;
    let invoiceResources;
    let binaryResource;

    const {
      general,
      signature,
      patient,
      practitioners,
      encounter,
      organization,
      invoice,
      chargeItems,
      binary
    } = req.body;

    patientResource = await generatePatientResource(currentTime, patient);

    for (const practitioner of practitioners) {
      resources.practitionerResources.push(
        await generatePractitionerResource(
          currentTime,
          practitioner,
          patient.doctors
        )
      );
    }

    organizationResource = await generateOrganizationResource(organization);

    for (const chargeItem of chargeItems) {
      resources.chargeItemResources.push(
        await generateChargeItemResource(
          chargeItem.type,
          chargeItem,
          patientResource,
          resources.practitionerResources
        )
      );
    }
    console.log(resources.chargeItemResources);

    binaryResource = await generateBinaryResource(binary);

    invoiceResources = await generateInvoiceResource(
      invoice,
      patientResource,
      resources.practitionerResources,
      organizationResource,
      resources.chargeItemResources,
      currentTime
    );



    encounterResource = await generateEncounterResource(
      currentTime,
      patientResource,
      [],
      encounter,
      [],
      general,
      patient.id
    );

    const entry = [
      await generateInvoiceComposition(
        general,
        currentTime,
        patientResource,
        patient.doctors,
        encounterResource,
        resources.practitionerResources,
        organizationResource,
        invoiceResources,
        resources.chargeItemResources,
        binaryResource
      ),
      ...resources.practitionerResources,
      organizationResource,
      patientResource,
      encounterResource,
      invoiceResources,
      ...resources.chargeItemResources,
      binaryResource
    ];

    return await generateFhirBundle(
      entry,
      currentTime,
      bundleId,
      general,
      signature
    );
  } catch (error) {
    throw error;
  }
};




const forwardToDiagnosticReportRecordBundle = async (req, bundleId, currentTime) => {
  try {
    // Create local resource arrays to prevent race conditions
    const resources = createResourceArrays();
    let patientResource;
    let encounterResource;
    let organizationResource;

    const { general, signature, patient, practitioners, encounter, organization, diagnosticReports, documentReferences } = req.body;

    patientResource = await generatePatientResource(currentTime, patient);

    for (const practitioner of practitioners) {
      resources.practitionerResources.push(await generatePractitionerResource(currentTime, practitioner, patient.doctors));
    }

    organizationResource = await generateOrganizationResource(organization);

    encounterResource = await generateEncounterResource(currentTime, patientResource, [], encounter, [], general, patient.id);
    for (const diagnosticReport of diagnosticReports) {
      resources.diagnosticReportResources.push(await generateDiagnosticReportResource(diagnosticReport, patientResource, resources.practitionerResources, organizationResource, diagnosticReport.categories, diagnosticReport.type, currentTime, encounterResource));
    }

    for (const documentReference of documentReferences) {
      resources.documentReferenceResources.push(await generateDocumentReferenceResource(documentReference.status, documentReference.docStatus, documentReference.type, documentReference.content, patientResource));
    }



    const entry = [
      await generateDiagnosticReportComposition(
        general,
        currentTime,
        patientResource,
        patient.doctors,
        encounterResource,
        resources.practitionerResources,
        organizationResource,
        resources.diagnosticReportResources,
        resources.documentReferenceResources
      ),
      patientResource,
      organizationResource,
      encounterResource,
      ...resources.practitionerResources,
      ...resources.diagnosticReportResources,
      ...resources.documentReferenceResources
    ];

    return await generateFhirBundle(entry, currentTime, bundleId, general, signature);
  } catch (error) {
    throw error;
  }
};


const forwardToImmunizationRecordBundle = async (req, bundleId, currentTime) => {
  try {
    // Create local resource arrays to prevent race conditions
    const resources = createResourceArrays();
    let patientResource;
    let encounterResource;
    let organizationResource;

    const { general, signature, patient, practitioners, encounter, organization, immunizations, documentReferences } = req.body;

    patientResource = await generatePatientResource(currentTime, patient);

    for (const practitioner of practitioners) {
      resources.practitionerResources.push(await generatePractitionerResource(currentTime, practitioner, patient.doctors));
    }

    organizationResource = await generateOrganizationResource(organization);

    for (const immunization of immunizations) {
      resources.immunizationResources.push(await generateImmunizationResource(immunization.type, immunization, patientResource, patient.id));
    }

    // for (const recommendation of immunizationRecommendations) {
    //     immunizationRecommendationResources.push(await 
    //         generateImmunizationRecommendationResource(recommendation.type,recommendation, patientResource, organizationResource));
    // }
    // for (const documentReference of documentReferences) {
    //     documentReferenceResources.push(await generateDocumentReferenceResource(documentReference.status, documentReference.docStatus, documentReference.type, documentReference.content, patientResource));
    // }

    encounterResource = await generateEncounterResource(currentTime, patientResource, [], encounter, [], general, patient.id);

    const entry = [
      await generateImmunizationComposition(
        general,
        currentTime,
        patientResource,
        patient.doctors,
        encounterResource,
        resources.practitionerResources,
        organizationResource,
        resources.immunizationResources,
        resources.immunizationRecommendationResources,
        resources.documentReferenceResources,
      ),
      patientResource,
      organizationResource,
      encounterResource,
      ...resources.practitionerResources,
      ...resources.immunizationResources,
      ...resources.immunizationRecommendationResources,
      ...resources.documentReferenceResources,
    ];

    return await generateFhirBundle(entry, currentTime, bundleId, general, signature);
  } catch (error) {
    throw error;
  }
};


const forwardToDischargeSummaryRecordBundle = async (req, bundleId, currentTime) => {
  try {
    // Create local resource arrays to prevent race conditions
    const resources = createResourceArrays();
    let patientResource;
    let encounterResource;
    let organizationResource;
    let appointmentResource;

    const { general, signature, conditions, patient, practitioners, organization, encounter, serviceRequests, medicationStatements, medicationRequests, procedures, dischargeSummary, appointment } = req.body;

    patientResource = await generatePatientResource(currentTime, patient);

    for (const practitioner of practitioners) {
      resources.practitionerResources.push(await generatePractitionerResource(currentTime, practitioner, patient.doctors))
    }

    organizationResource = await generateOrganizationResource(organization);

    for (const allergyIntolerance of patient.allergyIntolerances) {
      resources.allergyIntoleranceResources.push(await generateAllergyIntoleranceResource(allergyIntolerance.type, allergyIntolerance.clinicalStatus, allergyIntolerance.verificationStatus, allergyIntolerance.notes, currentTime, patientResource, resources.practitionerResources, allergyIntolerance.doctor));
    }

    for (const serviceRequest of serviceRequests) {
      resources.serviceRequestResources.push(await generateServiceRequestResource(serviceRequest.status, serviceRequest.intent, serviceRequest.categories, serviceRequest.type, currentTime, patientResource, resources.practitionerResources, patient.doctors));
    }

    for (const medicationStatement of medicationStatements) {
      resources.medicationStatementResources.push(await generateMedicationStatementResource(medicationStatement.status, medicationStatement.type, currentTime, patientResource));
    }

    for (const procedure of procedures) {
      resources.procedureResources.push(await generateProcedureResource(procedure.status, procedure.type, procedure.performedDateTime, procedure.followUp, patientResource, patient.id));
    }

    for (const documentReference of dischargeSummary) {
      resources.dischargeSummaryResources.push(await generateDocumentReferenceResource(documentReference.status, documentReference.docStatus, documentReference.type, documentReference.content, patientResource));
    }

    for (const cond of conditions) {
      resources.conditionResources.push(await generateConditionResource(cond.type, cond.status, patientResource, cond));
    }

    encounterResource = await generateEncounterResource(currentTime, patientResource, resources.conditionResources, encounter, conditions, general, patient.id);

    for (const medicationRequest of medicationRequests) {
      resources.medicationRequestResources.push(await generateMedicationRequestResource(medicationRequest.status, medicationRequest.intent, medicationRequest.authoredOn, medicationRequest.medication, medicationRequest.forCondition, medicationRequest.reason, medicationRequest.dosageInstruction, patientResource, resources.practitionerResources, resources.conditionResources, patient.doctors));
    }

    appointmentResource = await generateAppointmentResource(appointment, patientResource, resources.practitionerResources, resources.serviceRequestResources, resources.conditionResources, patient.doctors)

    const entry = [
      await generateOpConsultComposition(
        general,
        currentTime,
        patientResource,
        patient.doctors,
        encounterResource,
        resources.practitionerResources,
        organizationResource,
        resources.allergyIntoleranceResources,
        resources.conditionResources,
        resources.serviceRequestResources,
        resources.medicationStatementResources,
        resources.medicationRequestResources,
        resources.procedureResources,
        resources.dischargeSummaryResources,
        appointmentResource
      ),
      ...resources.practitionerResources,
      organizationResource,
      patientResource,
      encounterResource,
      ...resources.allergyIntoleranceResources,
      appointmentResource,
      ...resources.conditionResources,
      ...resources.procedureResources,
      ...resources.serviceRequestResources,
      ...resources.medicationStatementResources,
      ...resources.medicationRequestResources,
      ...resources.dischargeSummaryResources,
    ];

    return await generateFhirBundle(entry, currentTime, bundleId, general, signature);
  } catch (error) {
    throw error;
  }
};

const forwardToOPConsultRecordBundle = async (req, bundleId, currentTime) => {
  try {
    // Create local resource arrays to prevent race conditions
    const resources = createResourceArrays();
    let patientResource;
    let encounterResource;
    let organizationResource;
    let appointmentResource;

    const { general, signature, conditions, patient, practitioners, organization, encounter, serviceRequests, medicationStatements, medicationRequests, procedures, documentReferences, appointment, familyHistory, vitals } = req.body;

    patientResource = await generatePatientResource(currentTime, patient);

    for (const practitioner of practitioners) {
      resources.practitionerResources.push(await generatePractitionerResource(currentTime, practitioner, patient.doctors))
    }


    organizationResource = await generateOrganizationResource(organization);

    for (const allergyIntolerance of patient.allergyIntolerances) {
      resources.allergyIntoleranceResources.push(await generateAllergyIntoleranceResource(allergyIntolerance.type, allergyIntolerance.clinicalStatus, allergyIntolerance.verificationStatus, allergyIntolerance.notes, currentTime, patientResource, resources.practitionerResources, allergyIntolerance.doctor));
    }

    for (const serviceRequest of serviceRequests) {
      resources.serviceRequestResources.push(await generateServiceRequestResource(serviceRequest.status, serviceRequest.intent, serviceRequest.categories, serviceRequest.type, currentTime, patientResource, resources.practitionerResources, patient.doctors, patient.id));
    }

    for (const medicationStatement of medicationStatements) {
      resources.medicationStatementResources.push(await generateMedicationStatementResource(medicationStatement.status, medicationStatement.type, currentTime, patientResource));
    }

    for (const procedure of procedures) {
      resources.procedureResources.push(await generateProcedureResource(procedure.status, procedure.type, procedure.performedDateTime, procedure.followUp, patientResource, patient.id));
    }

    for (const documentReference of documentReferences) {
      resources.documentReferenceResources.push(await generateDocumentReferenceResource(documentReference.status, documentReference.docStatus, documentReference.type, documentReference.content, patientResource));
    }

    for (const cond of conditions) {
      resources.conditionResources.push(await generateConditionResource(cond.type, cond.status, patientResource, cond, patient.id));
    }

    // Process family history if available
    for (const familyHistoryItem of familyHistory || []) {
      resources.familyMemberHistoryResources.push(await generateFamilyMemberHistoryResource(familyHistoryItem, patientResource));
    }


    // Process vitals to generate observation resources if available
    if (vitals && Object.keys(vitals).length > 0) {
      const vitalObservations = await generateObservationResourcesFromVitals(
        vitals,
        currentTime,
        patientResource,
        resources.practitionerResources,
        organizationResource,
        patient.doctors
      );
      resources.observationResources.push(...vitalObservations);
    }


    encounterResource = await generateEncounterResource(currentTime, patientResource, resources.conditionResources, encounter, conditions, general, patient.id);



    for (const medicationRequest of medicationRequests) {
      resources.medicationRequestResources.push(await generateMedicationRequestResource(medicationRequest.status, medicationRequest.intent, medicationRequest.authoredOn, medicationRequest.medication, medicationRequest.forCondition, medicationRequest.reason, medicationRequest.dosageInstruction, patientResource, resources.practitionerResources, resources.conditionResources, patient.doctors, patient.id));
    }

    appointmentResource = await generateAppointmentResource(appointment, patientResource, resources.practitionerResources, resources.serviceRequestResources, resources.conditionResources, patient.doctors)

    const entry = [
      await generateOpConsultComposition(
        general,
        currentTime,
        patientResource,
        patient.doctors,
        encounterResource,
        resources.practitionerResources,
        organizationResource,
        resources.allergyIntoleranceResources,
        resources.conditionResources,
        resources.serviceRequestResources,
        resources.medicationStatementResources,
        resources.medicationRequestResources,
        resources.procedureResources,
        resources.documentReferenceResources,
        appointmentResource,
        resources.familyMemberHistoryResources,
        resources.observationResources
      ),
      ...resources.practitionerResources,
      organizationResource,
      patientResource,
      encounterResource,
      ...resources.allergyIntoleranceResources,
      ...resources.familyMemberHistoryResources,
      ...resources.observationResources,
      appointmentResource,
      ...resources.conditionResources,
      ...resources.procedureResources,
      ...resources.serviceRequestResources,
      ...resources.medicationStatementResources,
      ...resources.medicationRequestResources,
      ...resources.documentReferenceResources,
    ];

    return await generateFhirBundle(entry, currentTime, bundleId, general, signature);
  } catch (error) {
    console.log("error forwarding op ", error)
    throw error;
  }
};

const forwardToPrescriptionRecordBundle = async (req, bundleId, currentTime) => {
  try {
    // Create local resource arrays to prevent race conditions
    const resources = createResourceArrays();
    let patientResource;
    let encounterResource;
    let organizationResource;
    let binaryResource;

    const { general, signature, patient, practitioners, conditions, encounter, organization, medicationRequests, binary } = req.body;

    patientResource = await generatePatientResource(currentTime, patient);

    for (const practitioner of practitioners) {
      resources.practitionerResources.push(await generatePractitionerResource(currentTime, practitioner, patient.doctors))
    }

    organizationResource = await generateOrganizationResource(organization);

    for (const cond of conditions) {
      resources.conditionResources.push(await generateConditionResource(cond.type, cond.status, patientResource, cond, patient.id));
    }

    for (const medicationRequest of medicationRequests) {
      resources.medicationRequestResources.push(await generateMedicationRequestResource(medicationRequest.status, medicationRequest.intent, medicationRequest.authoredOn, medicationRequest.medication, medicationRequest.forCondition, medicationRequest.reason, medicationRequest.dosageInstruction, patientResource, resources.practitionerResources, resources.conditionResources, patient.doctors, patient.id));
    }

    encounterResource = await generateEncounterResource(currentTime, patientResource, resources.conditionResources, encounter, conditions, general, patient.id);

    binaryResource = await generateBinaryResource(binary);

    const entry = [
      await generatePrescriptionComposition(
        general,
        currentTime,
        patientResource,
        patient.doctors,
        encounterResource,
        resources.practitionerResources,
        organizationResource,
        resources.conditionResources,
        resources.medicationRequestResources,
        binaryResource
      ),
      patientResource,
      organizationResource,
      encounterResource,
      ...resources.practitionerResources,
      ...resources.conditionResources,
      ...resources.medicationRequestResources,
      binaryResource
    ];

    return await generateFhirBundle(entry, currentTime, bundleId, general, signature);
  } catch (error) {
    throw error;
  }
};



const forwardToHealthDocumentRecordBundle = async (req, bundleId, currentTime) => {
  try {
    // Create local resource arrays to prevent race conditions
    const resources = createResourceArrays();
    let patientResource;
    let organizationResource;

    const { general, signature, patient, practitioners, organization, documentReferences } = req.body;

    patientResource = await generatePatientResource(currentTime, patient);

    for (const practitioner of practitioners) {
      resources.practitionerResources.push(await generatePractitionerResource(currentTime, practitioner, patient.doctors))
    }

    organizationResource = await generateOrganizationResource(organization);

    // Ensure documentReferences is an array and process each document reference
    const documentReferencesArray = documentReferences || [];
    for (const documentReference of documentReferencesArray) {
      resources.documentReferenceResources.push(await generateDocumentReferenceResource(documentReference.status, documentReference.docStatus, documentReference.type, documentReference.content, patientResource));
    }

     

    const entry = [
      await generateHealthDocumentComposition(
        general,
        currentTime,
        patientResource,
        patient.doctors,
        resources.practitionerResources,
        organizationResource,
        resources.documentReferenceResources
      ),
      ...resources.practitionerResources,
      organizationResource,
      patientResource,
      ...resources.documentReferenceResources,
    ]

    return await generateFhirBundle(entry, currentTime, bundleId, general, signature);
  } catch (error) {
    throw error;
  }
};

const forwardToWellnessRecordBundle = async (req, bundleId, currentTime) => {
  try {
    // Create local resource arrays to prevent race conditions
    const resources = createResourceArrays();
    let patientResource;

    const { general, signature, patient, practitioners, observations, doctor } = req.body;

    patientResource = await generatePatientResource(currentTime, patient);
    for (const practitioner of practitioners) {
      resources.practitionerResources.push(await generatePractitionerResource(currentTime, practitioner, patient.doctors))
    }

    
    for (const obs of observations) {
      resources.observationResources.push(await generateObservationResource(obs, currentTime, patientResource, patient.doctors, resources.practitionerResources, null));
    }

    const entry = [
      await generateWellnessComposition(
        general,
        currentTime,
        patientResource,
        resources.practitionerResources,
        resources.observationResources
      ),
      patientResource,
      ...resources.practitionerResources,
      ...resources.observationResources
    ];

    return await generateFhirBundle(entry, currentTime, bundleId, general, signature);
  } catch (error) {
    throw error;
  }
};

const generateFhirBundle = async (entry, currentTime, bundleId, general, signature) => {
  const whichResource = entry.find(resource => resource.resource?.resourceType?.toLowerCase() === signature.who.type.toLowerCase());
  const id = uuidv4();
  return {
    resourceType: 'Bundle',
    id: id,
    meta: bundleMetadata(currentTime),
    identifier: bundleIdentifier(general.hipUrl, general.clientId),
    type: 'document',
    timestamp: currentTime,
    entry,
    signature: {
      type: signatureConstant(),
      when: currentTime,
      who: whichResource ? {
        reference: `urn:uuid:${whichResource.resource.id}`,
        display: whichResource.resource.resourceType
      } : null,
      sigFormat: signature.sigFormat,
      data: signature.data
    }
  };
}

// ABDM HIU Data Transfer Format Wrapper
// Formats FHIR bundle according to ABDM HIU API requirements
export const formatForABDMTransfer = (fhirBundle, transactionId, hipId) => {
  return {
    dataRequest: {
      transactionId: transactionId,
      status: "SUCCESS",
      hip: {
        id: hipId
      },
      entries: [
        {
          content: JSON.stringify(fhirBundle),
          mediaType: "application/json"
        }
      ]
    }
  };
};