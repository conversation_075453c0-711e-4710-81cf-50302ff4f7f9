import mongoose from "mongoose";
import { patientSchema } from "healtether.models/clinics.schema/patient.schema.js";
import { clinicSchema } from "healtether.models/clinics.schema/client.schema.js";
import { staffSchema } from "healtether.models/clinics.schema/staff.schema.js";
import { userSchema } from "healtether.models/clinics.schema/user.schema.js";
import { prescriptionSchema } from "healtether.models/clinics.schema/prescription/prescription.schema.js";
import { procedureSchema } from "healtether.models/clinics.schema/prescription/past-procedure.schema.js";
import { OPConsultRecordSchema } from "../schema/fhir_schema/op_consult.schema.fhir.js";
import { PrescriptionRecordSchema } from "../schema/fhir_schema/prescription.schema.fhir.js";
import { HealthDocumentRecordSchema } from "../schema/fhir_schema/health_document.schema.fhir.js";
import { DischargeSummaryRecordSchema } from "../schema/fhir_schema/discharge_summary.schema.fhir.js";
import { DiagnosticReportSchema } from "../schema/fhir_schema/diagnostic_report_lab.schema.fhir.js";
import { InvoiceRecordSchema } from "../schema/fhir_schema/invoice.schema.fhir.js";
import { WellnessRecordSchema } from "../schema/fhir_schema/wellness.schema.fhir.js";
import { ImmunizationRecordSchema } from "../schema/fhir_schema/immunization.schema.fhir.js";
// import { userSchema, MapClinicUserSchema } from "healtether.models/clinics.schema/user.schema.js";

import { clinicGroupSchema } from "healtether.models/clinics.schema/clinicgroup.schema.js";
import { abhaConsentSchema } from "../schema/consent.schema.js";
import { tokenSchema } from "../schema/token.schema.js";
import dotenv from "dotenv";
dotenv.config(); // ✅ Load environment variables

mongoose.set("strictQuery", false);

// Get MongoDB URI from environment
const portalDbConnectionString = process.env.MONGODB_PORTAL_URI;
if (!portalDbConnectionString) {
  console.error(
    "❌ MONGODB_PORTAL_URI is not defined in environment variables"
  );
  process.exit(1); // Exit the process if no DB URI is provided
}

// Create a separate Mongoose connection
const portalDb = mongoose.createConnection(portalDbConnectionString);

// Register models
portalDb.model("Patient", patientSchema);
portalDb.model("Client", clinicSchema);
portalDb.model("ClinicGroup", clinicGroupSchema);
portalDb.model("Staff", staffSchema);
portalDb.model("User", userSchema);
portalDb.model("Prescription", prescriptionSchema);
portalDb.model("Pastprocedures", procedureSchema);
// portalDb.model("LinkClientUser", MapClinicUserSchema);
portalDb.model("abhaConsents", abhaConsentSchema);
portalDb.model("Token", tokenSchema);
portalDb.model("OPConsultFHIRRecord", OPConsultRecordSchema);
portalDb.model("DischargeSummaryFHIRRecord", DischargeSummaryRecordSchema);
portalDb.model("PrescriptionFHIRRecord", PrescriptionRecordSchema);
portalDb.model("HealthDocumentFHIRRecord", HealthDocumentRecordSchema);
portalDb.model("DiagnosticReportFHIRRecord", DiagnosticReportSchema);
portalDb.model("InvoiceReportFHIRRecord", InvoiceRecordSchema);
portalDb.model("WellnessReportFHIRRecord", WellnessRecordSchema);
portalDb.model("ImmunizationReportFHIRRecord",ImmunizationRecordSchema)
// Handle connection events (except in Jest)
if (process.env.ENV !== "jest") {
  portalDb.on("connected", () => console.log("✅ Portal DB connected"));
  portalDb.on("reconnected", () => console.log("♻️ Portal DB reconnected"));
  portalDb.on("disconnected", () => console.log("⚠️ Portal DB disconnected"));
  portalDb.on("error", (err) =>
    console.error("❌ Portal DB connection error:", err)
  );
}

// ✅ Properly close the connection
export async function CloseConnection() {
  if (portalDb.readyState === 1) await portalDb.close(); // Close the connection properly
}

export default portalDb;
