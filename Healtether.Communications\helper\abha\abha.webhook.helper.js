import mongoose from "mongoose";
import { PROFILE_ON_SHARE } from "../../utils/abha.api.js";
import { AddAppointmentOnScanAndShare } from "../clinic/clinic.helper.js";
import { ackProfileShare } from "./abdm.helper.js";
import portalDb from "../../config/clinics.collections.config.js";
import moment from "moment";
const tokenModel = portalDb.model("Token");
export const profileOnShare = async (req) => {
    const body = req.body;
   const tokenNumber = await getNextTokenNumber(`${body?.metaData?.context}`);

  const headers = { ...req.headers };

  if (!body) {
    return {
      isSuccess: false,
      data: { error: { message: "Invalid request body" } },
    };
  }

  const payload = {
    acknowledgement: {
      abhaAddress: body.profile.patient.abhaAddress,
      status: "SUCCESS",
      profile: {
        context: body.metaData.context,
        tokenNumber: tokenNumber||"15",
        expiry: 3600,
      },
    },
    response: {
      requestId: headers["request-id"],
    },
  };

  const result = await ackProfileShare(PROFILE_ON_SHARE, req, payload);
  // console.log("result",await result.json());
  console.log("result", await result);

  if (result.status === 202 && result) {
    const clinicResponse = await AddAppointmentOnScanAndShare(body,tokenNumber);
    console.log("Appointment created successfully in Clinic:", clinicResponse);

    return clinicResponse;
  } else {
    return {
      isSuccess: false,
      data: {
        error: { message: "Failed to get a valid result from ABDM Gateway" },
      },
    };
  }
};



 export const getCurrentTokenNumber=async(clinicId)=> {
  const today = moment().startOf('day').toDate();
    const counter = await tokenModel.findOne({ clinic: clinicId, data:today });
    return counter ? counter.value : 0;
  }

   export const  getNextTokenNumber=async(clinicId)=> {
      const today = moment().startOf('day').toDate();
    let newmongooseclinicId =clinicId&&new mongoose.Types.ObjectId(clinicId);
    const counter = await tokenModel.findOneAndUpdate(
      {clinic:  newmongooseclinicId ,date:today},
      { $inc: { value: 1 } },
      { new: true, upsert: true }
    );
    return counter.value;
  }