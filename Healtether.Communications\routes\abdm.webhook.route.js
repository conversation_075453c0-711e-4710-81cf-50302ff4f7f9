import { Router } from "express";
import { profileShareHook } from "../controllers/abha.webhook.controller.js";
import fs from "fs";
import path from "path";
import {
  CONSENT_ON_INIT,
  HIP_CONSENT_NOTIFY,
  HIU_CONSENT_NOTIFY,
  PROFILE_SHARE_WEBHOOK,
  PROFILE_ON_SHARE,
  CONSENT_ON_STATUS,
  CONSENT_REQUEST_ON_FETCH,
  DATA_FLOW_HEALTHINFORMATION_ON_REQUEST,
  DATA_PUSH_URL,
  DISCOVER_HEALTH_RECORDS,
  ON_GENERATE_TOKEN,
  ON_LINK_CARECONTEXT,
  INIT_CARECONTEXT,
  CONFIRM_INIT,
  REQUEST_M2,
  ON_REQUEST_M2,
  ON_GENERATE_TOKEN2,
} from "../utils/abha.api.js";
import { abdmWebHook } from "../middleware/abdm.webhook.middleware.js";
import {
  storeWebhookResponseForHIUAndHIP,
  checkConsentStatusHIP,
  updateConsentStatus,
} from "../controllers/abha.milestone.three.controller.js";
import { sendHealthInformationRequest } from "../helper/abha/abha.milestone.three.helper.js";
import {
  decryptData,
  generateKeyMaterialCore,
} from "../controllers/fidelius.controller.js";
import portalDb from "../config/clinics.collections.config.js";
import {
  discoverCareContext,
  onConfirm,
  onInitCareContext,
  onNotifyConsentM2,
  onRequestM2,
} from "../helper/abha/milestone.two.helper.js";
import { HealthInfoTransfer } from "../controllers/abha.milestone.two.js";
const AbhaConsentsModel = portalDb.model("abhaConsents");
const PatientsModel = portalDb.model("Patient");
export const abdmWebHookRouter = Router();

abdmWebHookRouter
  .route(PROFILE_ON_SHARE)
  .post(abdmWebHook, async (req, res, next) => {
    try {
      res.status(200);
      return await profileShareHook(req, res);
    } catch (error) {
      next(error);
    }
  });

abdmWebHookRouter
  .route(PROFILE_SHARE_WEBHOOK)
  .post(abdmWebHook, async (req, res, next) => {
    try {
      return await profileShareHook(req, res);
    } catch (error) {
      next(error);
    }
  });

abdmWebHookRouter.route(CONSENT_ON_INIT).post(async (req, res, next) => {
  try {
    console.log("CONSENT_ON_INIT", req.body);
    return await checkConsentStatusHIP(req, res);
  } catch (error) {
    next(error);
  }
});
abdmWebHookRouter
  .route(CONSENT_ON_STATUS)
  .post(abdmWebHook, async (req, res, next) => {
    try {
      console.log("CONSENT_ON_STATUS", req.body);
      let result = await updateConsentStatus(req.body);
      res.status(202).json(result);
    } catch (error) {
      next(error);
    }
  });

abdmWebHookRouter
  .route(HIU_CONSENT_NOTIFY)
  .post(abdmWebHook, async (req, res, next) => {
    try {
      const consentReqId = req.body.notification.consentRequestId;
      if (
        req?.body?.notification?.status == "DENIED" ||
        req?.body?.notification?.status == "REVOKED"
      ) {
        await AbhaConsentsModel.updateOne(
          { consentRequestId: consentReqId },
          {
            $set: {
              consentStatus: req.body.notification.status,
              consentStatusUpdatedOn: new Date().toISOString(),
            },
          }
        );
        return res.status(202).json({
          message: req.body?.notification?.reason || "Consent status updated",
        });
      }

      const consentId = req.body.notification.consentArtefacts[0].id;
      storeWebhookResponseForHIUAndHIP(
        consentId,
        "hiu",
        req.body,
        req.headers,
        consentReqId
      );
      res.status(202).json({ message: "HIU Notification received" });
    } catch (error) {
      next(error);
    }
  });

abdmWebHookRouter
  .route(HIP_CONSENT_NOTIFY)
  .post(abdmWebHook, async (req, res, next) => {
    try {
      console.log(
        "notify on pull record",
        req.body,
        req.headers,
        req.headers["request-id"]
      );
      if (req.body.notification.status == "REVOKED") {
        return;
      }
      const requestId = req.headers["request-id"];
      const consentId = req.body.notification.consentId;
      storeWebhookResponseForHIUAndHIP(
        consentId,
        "hip",
        req.body,
        req.headers,
        requestId
      );
      res.status(202).json({ message: "HIP Notification received" });
    } catch (error) {
      next(error);
    }
  });

abdmWebHookRouter
  .route(CONSENT_REQUEST_ON_FETCH)
  .post(abdmWebHook, async (req, res, next) => {
    try {
      console.log("CONSENT_REQUEST_ON_FETCH", req.body);
      sendHealthInformationRequest(req.body);
      res.status(202).json({ message: "HIU Notification received" });
    } catch (error) {
      next(error);
    }
  });

abdmWebHookRouter
  .route(ON_REQUEST_M2)
  .post(abdmWebHook, async (req, res, next) => {
    try {
      console.log("on-request_m21", req.body);
      if (req.body.error) {
        return res.status(400).json({ message: "hip is not responding" });
      }

      await AbhaConsentsModel.findOneAndUpdate(
        { "consentArtefacts.requestId": req.body.response.requestId },
        {
          $set: {
            "consentArtefacts.$.transactionId":
              req.body.hiRequest.transactionId,
          },
        },
        { new: true }
      );
    } catch (error) {
      next(error);
    }
  });

abdmWebHookRouter
  .route(DATA_PUSH_URL)
  .post(abdmWebHook, async (req, res, next) => {
    try {
      // console.log("DATA_PUSH_URL", req.body);
      if (
        !req.body.entries ||
        !Array.isArray(req.body.entries) ||
        req.body.entries.length === 0
      ) {
        return res.status(400).json({ message: "No entries found" });
      } else if (req.body.error) {
        return res.status(400).json({ message: "Error in entries" });
      }
      // Option 1: Find the document first, then filter the array in JavaScript
      const consent = await AbhaConsentsModel.findOne({
        "consentArtefacts.transactionId": req.body.transactionId,
      });

      // If found, filter to get the specific array item
      const matchingArtefact = consent?.consentArtefacts.find(
        (artefact) => artefact.transactionId === req.body.transactionId
      );

      console.log("matchingArtefact", matchingArtefact);
      const processPromises = req.body.entries.map(async (entry) => {
        let data = {
          encryptedData: entry.content,
          receiverPrivateKey: matchingArtefact.privateKey, // need to fetch from db
          receiverNonce: matchingArtefact.nonce, // need to fetch from db
          senderPublicKey: req.body.keyMaterial.dhPublicKey.keyValue,
          senderNonce: req.body.keyMaterial.nonce,
        };
        return decryptData(data, consent._id);
      });

      await Promise.all(processPromises);
      res.status(202).json({ message: "HIU Notification received" });
    } catch (error) {
      next(error);
    }
  });

abdmWebHookRouter
  .route(DATA_FLOW_HEALTHINFORMATION_ON_REQUEST)
  .post(abdmWebHook, async (req, res, next) => {
    try {
      console.log("DATA_FLOW_HEALTHINFORMATION_ON_REQUEST", req.body);
      /// this callback will have transaction id that we can use to differ the content we are getting from webhook
      res.status(202).json({ message: "HIU Notification received" });
    } catch (error) {
      next(error);
    }
  });

abdmWebHookRouter
  .route(ON_GENERATE_TOKEN)
  .post(abdmWebHook, async (req, res, next) => {
    try {
      console.log("ON_GENERATE_TOKEN", req.body);
      const updatedpatient = await PatientsModel.findOneAndUpdate(
        { abhaAddress: req.body.abhaAddress },
        { linkingToken: req.body.linkToken },
        { new: true, runValidators: true }
      );
    } catch (error) {
      next(error);
    }
  });

  abdmWebHookRouter
  .route(ON_GENERATE_TOKEN2)
  .post(abdmWebHook, async (req, res, next) => {
    try {
      console.log("ON_GENERATE_TOKEN", req.body);
      const updatedpatient = await PatientsModel.findOneAndUpdate(
        { abhaAddress: req.body.abhaAddress },
        { linkingToken: req.body.linkToken },
        { new: true, runValidators: true }
      );
    } catch (error) {
      next(error);
    }
  });
  

abdmWebHookRouter
  .route(ON_LINK_CARECONTEXT)
  .post(abdmWebHook, async (req, res, next) => {
    try {
      console.log("ON_LINK_CARECONTEXT", req.body);
    } catch (error) {
      next(error);
    }
  });

abdmWebHookRouter
  .route(DISCOVER_HEALTH_RECORDS)
  .post(abdmWebHook, async (req, res, next) => {
    try {
      console.log(req.body);
      await discoverCareContext(req.body, req.headers["request-id"]);
    } catch (error) {
      next(error);
    }
  });

abdmWebHookRouter
  .route(INIT_CARECONTEXT)
  .post(abdmWebHook, async (req, res, next) => {
    console.log("INIT_CARECONTEXT", req.body);
    try {
      await onInitCareContext(req.body, req.headers["request-id"]);
    } catch (error) {
      next(error);
    }
  });

abdmWebHookRouter
  .route(CONFIRM_INIT)
  .post(abdmWebHook, async (req, res, next) => {
    try {
      console.log(req.body);
      await onConfirm(req.body, req.headers["request-id"]);
    } catch (error) {
      next(error);
    }
  });

abdmWebHookRouter
  .route(REQUEST_M2)
  .post(abdmWebHook, async (req, res, next) => {
    try {
      console.log("request callback1234567890", req.body);
      let data = {
        transactionId: req.body.transactionId,
        requestId: req.headers["request-id"],
      };
      
      await onRequestM2(data);
      await HealthInfoTransfer(req.body, res);
    } catch (error) {
      next(error);
    }
  });
